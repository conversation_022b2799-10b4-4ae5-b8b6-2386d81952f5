import sys
import os
import configparser
import re
import platform
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QLineEdit, QRadioButton, QButtonGroup, QCheckBox, QSpinBox,
    QGroupBox, QComboBox, QTextEdit, QFileDialog, QFormLayout
)
from PySide6.QtCore import QThread, Signal
import time
import funasr

class AsrGui(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("SenseVoice 语音识别客户端")
        self.setGeometry(100, 100, 900, 600)

        # 初始化主窗口组件
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        # 创建主布局（左右分栏）
        self.main_layout = QHBoxLayout(self.central_widget)
        self.main_layout.setContentsMargins(10, 10, 10, 10)  # 设置外边距
        self.main_layout.setSpacing(15)  # 左右面板之间的间距

        # 创建左右面板
        self.left_panel = QWidget()
        self.right_panel = QWidget()
        
        # 设置左右面板的布局
        self.left_layout = QVBoxLayout(self.left_panel)
        self.left_layout.setContentsMargins(5, 10, 5, 10)  # 减少左右边距
        self.right_layout = QVBoxLayout(self.right_panel)
        
        # 将左右面板添加到主布局
        self.main_layout.addWidget(self.left_panel, 40)  # 左侧面板占据40%宽度
        self.main_layout.addWidget(self.right_panel, 60)  # 右侧面板占据60%宽度

        # 模型缓存和配置数据存储
        self.asr_models_config = {}
        self.vad_models_config = {}
        self.model_cache = {}

        # --- 左侧控制面板组件构建 ---
        # 标题和描述
        self.title_label = QLabel("SenseVoice 语音识别 \n(带时间戳和情感识别)")
        self.title_label.setStyleSheet("font-size: 16pt; font-weight: bold;")
        
        self.description_label = QLabel("上传音频文件，选择运行设备和模型进行识别。支持情感标签识别、事件检测和多种输出格式。")
        self.description_label.setWordWrap(True)
        
        # 将标题和描述添加到左侧面板
        self.left_layout.addWidget(self.title_label)
        self.left_layout.addWidget(self.description_label)
        self.left_layout.addSpacing(15)

        # 音频文件选择
        self.audio_path_edit = QLineEdit()
        self.audio_path_edit.setPlaceholderText("请选择音频文件路径")
        self.audio_path_edit.setReadOnly(True)
        
        self.browse_button = QPushButton("浏览文件")
        self.browse_button.clicked.connect(self.browse_audio_file)
        
        # 创建文件选择布局
        audio_input_layout = QHBoxLayout()
        audio_input_layout.addWidget(self.audio_path_edit)
        audio_input_layout.addWidget(self.browse_button)
        self.left_layout.addLayout(audio_input_layout)
        self.left_layout.addSpacing(15)

        # 基础配置区域（设备选择和输出模式）
        basic_config_layout = QVBoxLayout()
        
        # 第一行：设备选择
        device_layout = QHBoxLayout()
        self.device_label = QLabel("运行设备:")
        self.device_combo = QComboBox()
        self.device_combo.addItem("CPU", "cpu")
        self.device_combo.addItem("CUDA", "cuda")
        
        # 根据操作系统设置默认设备
        is_mac = platform.system().lower() == 'darwin'
        default_device_index = 0 if is_mac else 1  # Mac默认CPU(0)，其他系统默认CUDA(1)
        self.device_combo.setCurrentIndex(default_device_index)
        
        device_layout.addWidget(self.device_label)
        device_layout.addWidget(self.device_combo)
        device_layout.addStretch()
        
        # 第二行：输出模式
        output_mode_layout = QHBoxLayout()
        self.output_mode_label = QLabel("输出模式:")
        self.output_mode_combo = QComboBox()
        self.output_mode_combo.addItem("时间戳格式 (SRT)", "timestamp")
        self.output_mode_combo.addItem("普通文本", "normal")
        self.output_mode_combo.setCurrentIndex(0)  # 默认选择timestamp
        
        output_mode_layout.addWidget(self.output_mode_label)
        output_mode_layout.addWidget(self.output_mode_combo)
        output_mode_layout.addStretch()
        
        basic_config_layout.addLayout(device_layout)
        basic_config_layout.addLayout(output_mode_layout)
        self.left_layout.addLayout(basic_config_layout)
        self.left_layout.addSpacing(10)

        # 模型选择
        self.model_config_group = QGroupBox("模型配置")
        model_config_layout = QFormLayout(self.model_config_group)
        model_config_layout.setVerticalSpacing(15)  # 增加垂直间距
        model_config_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.AllNonFixedFieldsGrow)  # 允许字段增长
        
        # 设置模型选择下拉框
        self.asr_model_combo = QComboBox()
        self.asr_model_combo.setSizeAdjustPolicy(QComboBox.SizeAdjustPolicy.AdjustToContents)  # 根据内容调整大小
        self.asr_model_combo.setMinimumContentsLength(20)  # 设置最小显示长度
        
        self.vad_model_combo = QComboBox()
        self.vad_model_combo.setSizeAdjustPolicy(QComboBox.SizeAdjustPolicy.AdjustToContents)
        self.vad_model_combo.setMinimumContentsLength(20)
        
        model_config_layout.addRow("ASR 模型:", self.asr_model_combo)
        model_config_layout.addRow("VAD 模型:", self.vad_model_combo)
        
        self.model_config_group.setMinimumHeight(150)  # 设置最小高度确保显示所有选项
        self.left_layout.addWidget(self.model_config_group)
        self.left_layout.addSpacing(20)

        # 识别按钮
        self.submit_button = QPushButton("开始识别")
        self.submit_button.setFixedHeight(40)
        self.submit_button.setStyleSheet("font-size: 12pt;")
        self.submit_button.clicked.connect(self.start_recognition)
        self.left_layout.addWidget(self.submit_button)
        self.left_layout.addStretch(1)  # 在底部添加可伸展空间

        # --- 右侧显示面板 ---
        # 程序状态区域
        status_header_layout = QHBoxLayout()
        self.status_label = QLabel("程序状态")
        self.status_label.setStyleSheet("font-size: 14pt; font-weight: bold;")
        
        self.clear_status_button = QPushButton("清空状态")
        self.clear_status_button.setMaximumWidth(80)
        self.clear_status_button.clicked.connect(lambda: self.status_text_edit.clear())
        
        status_header_layout.addWidget(self.status_label)
        status_header_layout.addStretch()
        status_header_layout.addWidget(self.clear_status_button)
        
        self.status_text_edit = QTextEdit()
        self.status_text_edit.setReadOnly(True)
        self.status_text_edit.setPlaceholderText("程序状态信息将显示在这里...")
        self.status_text_edit.setMaximumHeight(200)  # 限制状态区域高度
        
        # 识别结果区域
        result_header_layout = QHBoxLayout()
        self.result_label = QLabel("识别结果")
        self.result_label.setStyleSheet("font-size: 14pt; font-weight: bold;")
        
        self.download_result_button = QPushButton("下载结果")
        self.download_result_button.setMaximumWidth(80)
        self.download_result_button.setEnabled(False)  # 初始禁用，有结果后启用
        self.download_result_button.clicked.connect(self.download_recognition_result)
        
        result_header_layout.addWidget(self.result_label)
        result_header_layout.addStretch()
        result_header_layout.addWidget(self.download_result_button)
        
        self.result_text_edit = QTextEdit()
        self.result_text_edit.setReadOnly(True)
        self.result_text_edit.setPlaceholderText("识别结果将显示在这里...")
        
        # 添加到右侧面板
        self.right_layout.addLayout(status_header_layout)
        self.right_layout.addWidget(self.status_text_edit)
        self.right_layout.addSpacing(10)  # 增加间距
        self.right_layout.addLayout(result_header_layout)
        self.right_layout.addWidget(self.result_text_edit)
        
        # 设置左侧面板的尺寸限制
        from PySide6.QtWidgets import QSizePolicy
        self.left_panel.setMinimumWidth(350)  # 增加控制面板最小宽度
        self.left_panel.setMaximumWidth(500)  # 增加控制面板最大宽度
        
        # 右侧面板应可伸展
        self.right_panel.setSizePolicy(
            QSizePolicy.Policy.Expanding,
            QSizePolicy.Policy.Expanding
        )
        
        # 存储当前识别结果的信息，用于下载
        self.current_recognition_result = ""
        self.current_audio_filename = ""
        self.current_output_mode = "timestamp"
        
        # 加载模型配置
        self.load_model_config()

    def browse_audio_file(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "选择音频文件", "", "音频文件 (*.wav *.mp3 *.flac *.m4a *.pcm)")
        if file_path:
            self.audio_path_edit.setText(file_path)
            self.status_text_edit.append(f"已选择音频文件: {file_path}")

    def load_model_config(self):
        # 统一的配置文件路径
        config_paths = [
            # os.path.join(os.getcwd(), "sensevoice", "model_mac.conf"),
            os.path.join(os.getcwd(), "sensevoice", "model.conf")
        ]
        
        config = configparser.ConfigParser()
        loaded_path = None
        for path in config_paths:
            if os.path.exists(path):
                try:
                    config.read(path, encoding='utf-8')
                    loaded_path = path
                    break
                except Exception as e:
                    self.status_text_edit.append(f"读取配置文件 {path} 失败: {e}")
                    continue

        if not loaded_path:
            self.status_text_edit.append("错误: 未找到配置文件。请确保 model.conf 或 model_mac.conf 位于 sensevoice 目录下。")
            self.asr_model_combo.addItem("错误: 未找到配置文件")
            self.vad_model_combo.addItem("错误: 未找到配置文件")
            return

        self.status_text_edit.append(f"成功从 {loaded_path} 加载模型配置。")

        def populate_combo(combo, section_name, model_dict):
            combo.clear()
            model_dict.clear()
            if section_name in config:
                for name, path_or_id in config[section_name].items():
                    combo.addItem(name)
                    model_dict[name] = path_or_id
                if not model_dict:
                     combo.addItem(f"无模型 (在 {section_name})")
            else:
                self.status_text_edit.append(f"警告: 配置文件中未找到 [{section_name}] 部分。")
                combo.addItem(f"无模型 (无 {section_name})")

        populate_combo(self.asr_model_combo, "asr_models_dir", self.asr_models_config)
        populate_combo(self.vad_model_combo, "vad_models_dir", self.vad_models_config)
        
        # 为VAD模型添加"不使用"选项
        if self.vad_model_combo.count() > 0 and "无模型" not in self.vad_model_combo.itemText(0) and "错误:" not in self.vad_model_combo.itemText(0):
            self.vad_model_combo.insertItem(0, "None (不使用)")
            self.vad_models_config["None (不使用)"] = None 
            # 默认选择第1个实际模型，而不是"None (不使用)"
            if self.vad_model_combo.count() > 1:
                self.vad_model_combo.setCurrentIndex(1)

    def start_recognition(self):
        audio_file = self.audio_path_edit.text()
        if not audio_file or not os.path.exists(audio_file):
            self.status_text_edit.append("错误: 请先选择一个有效的音频文件。")
            return

        device = self.device_combo.currentData()
        
        asr_model_name = self.asr_model_combo.currentText()
        vad_model_name = self.vad_model_combo.currentText()

        asr_model_path = self.asr_models_config.get(asr_model_name)
        vad_model_path = self.vad_models_config.get(vad_model_name)

        if not asr_model_path or "错误:" in asr_model_name or "无模型" in asr_model_name:
            self.status_text_edit.append("错误: 请选择一个有效的 ASR 模型。")
            return
        
        if "错误:" in vad_model_name or "无模型" in vad_model_name or vad_model_name == "None (不使用)": 
            vad_model_path = None

        output_mode = self.output_mode_combo.currentData()

        # 清空结果区域，准备显示新的识别结果
        self.result_text_edit.clear()
        self.result_label.setText("识别结果")
        self.download_result_button.setEnabled(False)  # 禁用下载按钮
        
        # 保存当前识别参数用于下载
        self.current_audio_filename = os.path.splitext(os.path.basename(audio_file))[0]
        self.current_output_mode = output_mode
        
        # 状态信息输出到状态区域
        self.status_text_edit.append(f"开始识别: {os.path.basename(audio_file)}")
        self.status_text_edit.append(f"  设备: {self.device_combo.currentText()}")
        self.status_text_edit.append(f"  ASR 模型: {asr_model_name}")
        self.status_text_edit.append(f"  VAD 模型: {vad_model_name if vad_model_path else '不使用'}")
        self.status_text_edit.append(f"  输出模式: {'时间戳格式 (SRT)' if output_mode == 'timestamp' else '普通文本'}")
        self.status_text_edit.append("")

        self.submit_button.setEnabled(False)
        self.submit_button.setText("正在识别中...")

        self.recognition_thread = RecognitionWorker(
            audio_file, device, 
            asr_model_path, vad_model_path,
            output_mode, self.model_cache
        )
        self.recognition_thread.progress.connect(self.update_progress)
        self.recognition_thread.finished.connect(self.recognition_finished)
        self.recognition_thread.error.connect(self.recognition_error)
        self.recognition_thread.start()

    def update_progress(self, message):
        self.status_text_edit.append(message)

    def recognition_finished(self, result_text, processing_time):
        # 状态信息更新
        self.status_text_edit.append(f"识别完成，耗时: {processing_time:.2f} 秒")
        
        # 识别结果显示
        self.result_text_edit.clear()
        self.result_label.setText(f"识别结果 (耗时: {processing_time:.2f} 秒)")
        self.result_text_edit.append(result_text)
        
        # 保存识别结果并启用下载按钮
        self.current_recognition_result = result_text
        self.download_result_button.setEnabled(True)
        
        self.submit_button.setEnabled(True)
        self.submit_button.setText("开始识别")

    def recognition_error(self, error_message):
        self.status_text_edit.append(f"--- 错误 ---")
        self.status_text_edit.append(error_message)
        self.submit_button.setEnabled(True)
        self.submit_button.setText("开始识别")

    def download_recognition_result(self):
        """下载识别结果到文件"""
        if not self.current_recognition_result:
            self.status_text_edit.append("错误: 没有可下载的识别结果。")
            return
        
        # 根据输出格式确定文件扩展名和过滤器
        if self.current_output_mode == "timestamp":
            default_filename = f"{self.current_audio_filename}.srt"
            file_filter = "SRT字幕文件 (*.srt);;所有文件 (*)"
        else:
            default_filename = f"{self.current_audio_filename}.txt"
            file_filter = "文本文件 (*.txt);;所有文件 (*)"
        
        # 打开保存对话框
        file_path, _ = QFileDialog.getSaveFileName(
            self, 
            "保存识别结果", 
            default_filename, 
            file_filter
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.current_recognition_result)
                self.status_text_edit.append(f"识别结果已保存到: {file_path}")
            except Exception as e:
                self.status_text_edit.append(f"保存文件失败: {str(e)}")

class RecognitionWorker(QThread):
    progress = Signal(str)
    finished = Signal(str, float)  # Added float for processing time
    error = Signal(str)

    def __init__(self, audio_file, device, 
                 asr_model_path, vad_model_path, output_mode, model_cache_dict):
        super().__init__()
        self.audio_file = audio_file
        self.device = device
        self.asr_model_path = asr_model_path
        self.vad_model_path = vad_model_path
        self.output_mode = output_mode
        self.model_cache = model_cache_dict

    def _ms2srt(self, ms):
        """时间戳格式化函数"""
        h = ms // 3600000
        m = (ms % 3600000) // 60000
        s = (ms % 60000) // 1000
        ms_r = ms % 1000
        return f"{h:02d}:{m:02d}:{s:02d},{ms_r:03d}"

    def run(self):
        try:
            self.progress.emit("正在准备模型...")
            model_key = f"{self.device}_{self.asr_model_path}_{str(self.vad_model_path)}"

            if model_key in self.model_cache:
                model = self.model_cache[model_key]
                self.progress.emit(f"从缓存加载模型")
            else:
                self.progress.emit(f"首次加载模型 (可能需要一些时间)")
                model_kwargs = {
                    "disable_update": True,
                    "device": self.device,
                    "model": self.asr_model_path
                }
                
                if self.vad_model_path:
                    model_kwargs["vad_model"] = self.vad_model_path
                    model_kwargs["vad_kwargs"] = {"max_single_segment_time": 30000}
                
                model = funasr.AutoModel(**model_kwargs)
                self.model_cache[model_key] = model
                self.progress.emit("模型加载完成.")

            self.progress.emit(f"开始识别音频文件: {os.path.basename(self.audio_file)}")
            
            generate_kwargs = {
                "input": self.audio_file,
                "cache": {},
                "language": "auto",
                "batch_size_s": 60,
                "merge_vad": True,
                "merge_length_s": 15,
                "output_timestamp": self.output_mode == "timestamp"
            }

            start_process_time = time.time()
            rec_result = model.generate(**generate_kwargs)
            end_process_time = time.time()
            processing_time = end_process_time - start_process_time
            self.progress.emit("原始识别结果获取完毕, 正在格式化...")
            
            output_lines = []
            
            # 普通模式
            if self.output_mode == "normal":
                texts = [r.get('text', '').strip() for r in rec_result if r.get('text')]
                output_lines = texts
            else:
                # 时间戳模式，构建 SRT 样式
                if rec_result:
                    raw = rec_result[0].get('text', '')
                    timestamps = rec_result[0].get('timestamp', [])
                    
                    # 情感和事件标签
                    emo_tags = {'<|HAPPY|>', '<|SAD|>', '<|ANGRY|>', '<|NEUTRAL|>', '<|FEARFUL|>', '<|DISGUSTED|>', '<|SURPRISED|>'}
                    event_tags = {'<|Speech|>', '<|BGM|>', '<|Applause|>', '<|Laughter|>', '<|Cry|>', '<|Sneeze|>', '<|Breath|>', '<|Cough|>', '<|Event_UNK|>'}
                    
                    parts = raw.split('<|zh|>')
                    offset = 0
                    segments = []
                    
                    for part in parts:
                        if not part:
                            continue
                        m = re.match(r'((?:<\|[^|]+\|>)+)', part)
                        if m:
                            tags = ''.join([t for t in re.findall(r'<\|[^|]+\|>', m.group(1)) if t in emo_tags or t in event_tags])
                            content = part[len(m.group(1)):].strip()
                        else:
                            tags = ''
                            content = part.strip()
                        
                        length = len(content)
                        if length == 0:
                            continue
                        
                        st = timestamps[offset][0] if offset < len(timestamps) else 0
                        et = timestamps[offset + length - 1][1] if offset + length - 1 < len(timestamps) else st
                        segments.append((tags + content, st, et))
                        offset += length
                    
                    # 格式化输出
                    for idx, (txt, st, et) in enumerate(segments, 1):
                        output_lines.append(str(idx))
                        output_lines.append(f"{self._ms2srt(st)} --> {self._ms2srt(et)}")
                        output_lines.append(txt)
                        output_lines.append("")
            
            if not output_lines:
                output_lines = ["识别结果为空。"]
            
            self.finished.emit("\n".join(output_lines), processing_time)

        except Exception as e:
            self.error.emit(f"识别过程中发生错误: {str(e)}\n请检查模型路径、音频文件格式和依赖项是否正确安装。\n错误详情: {type(e).__name__}: {e}")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = AsrGui()
    window.show()
    sys.exit(app.exec()) 