import gradio as gr
from funasr import AutoModel
import os
import configparser
import time
import re
import platform

# 时间戳格式化函数
def ms2srt(ms):
    h = ms // 3600000
    m = (ms % 3600000) // 60000
    s = (ms % 60000) // 1000
    ms_r = ms % 1000
    return f"{h:02d}:{m:02d}:{s:02d},{ms_r:03d}"

# 加载模型配置
config = configparser.ConfigParser()
config_paths = [
    # os.path.join(os.path.dirname(os.path.abspath(__file__)), 'model_mac.conf'),
    os.path.join(os.path.dirname(os.path.abspath(__file__)), 'model.conf')
]

loaded_config_path = None
for config_path in config_paths:
    if os.path.exists(config_path):
        try:
            config.read(config_path, encoding='utf-8')
            loaded_config_path = config_path
            break
        except Exception as e:
            print(f"读取配置文件 {config_path} 失败: {e}")

if not loaded_config_path:
    raise FileNotFoundError(f"配置文件未找到，检查路径: {config_paths}")

print(f"成功加载配置文件: {loaded_config_path}")

asr_models = dict(config['asr_models_dir']) if 'asr_models_dir' in config else {}
vad_models = dict(config['vad_models_dir']) if 'vad_models_dir' in config else {}

# 为VAD模型添加"不使用"选项
vad_models_with_none = {"None (不使用)": None}
vad_models_with_none.update(vad_models)

default_asr = list(asr_models.keys())[0] if asr_models else None
default_vad = list(vad_models.keys())[0] if vad_models else None

# 根据操作系统设置默认设备
is_mac = platform.system().lower() == 'darwin'
default_device = 'cpu' if is_mac else 'cuda'

# 模型实例缓存
model_cache = {}

def get_model(device, asr_name, vad_name):
    """获取或加载所选模型实例"""
    key = f"{device}_{asr_name}_{str(vad_name)}"
    if key not in model_cache:
        print(f"为配置 {key} 加载模型...")
        asr_path = asr_models.get(asr_name)
        vad_path = vad_models_with_none.get(vad_name)
        
        if not asr_path:
            raise ValueError(f"ASR 模型路径缺失: {asr_name}")
        
        params = {
            "disable_update": True,
            "device": device,
            "model": asr_path
        }
        
        if vad_path:  # 只有在VAD路径存在时才添加VAD相关参数
            params.update({
                "vad_model": vad_path,
                "vad_kwargs": {"max_single_segment_time": 30000}
            })
        
        model_cache[key] = AutoModel(**params)
        print(f"配置 {key} 的模型加载完成。")
    return model_cache[key]

# 主处理函数
def process_audio(audio_path, device, asr_choice, vad_choice, output_mode):
    if not audio_path:
        status_info = "错误: 请先上传一个音频文件。"
        return "", status_info, gr.update(label="识别结果")
    
    if not asr_choice:
        status_info = "错误: 请选择一个ASR模型。"
        return "", status_info, gr.update(label="识别结果")
    
    # 状态信息
    status_lines = [
        f"开始识别: {os.path.basename(audio_path)}",
        f"  设备: {device.upper()}",
        f"  ASR 模型: {asr_choice}",
        f"  VAD 模型: {vad_choice if vad_choice != 'None (不使用)' else '不使用'}",
        f"  输出模式: {'时间戳格式 (SRT)' if output_mode == 'timestamp' else '普通文本'}",
        "",
        "正在准备模型...",
    ]
    
    try:
        # 获取或加载模型
        model_config_id = f"{device}_{asr_choice}_{vad_choice}"
        if not hasattr(process_audio, 'current_model') or process_audio.current_model != model_config_id:
            status_lines.append(f"加载新模型组合: {model_config_id}")
            process_audio.model = get_model(device, asr_choice, vad_choice)
            process_audio.current_model = model_config_id
        else:
            status_lines.append("从缓存加载模型")
        
        model = process_audio.model
        status_lines.append("模型加载完成.")
        status_lines.append(f"开始识别音频文件: {os.path.basename(audio_path)}")
        
        gen_kwargs = {
            "input": audio_path,
            "cache": {},
            "language": "auto",
            "batch_size_s": 60,
            "merge_vad": True,
            "merge_length_s": 15,
            "output_timestamp": output_mode == "timestamp",
        }
        
        # 记录识别开始时间
        start_process_time = time.time()
        res = model.generate(**gen_kwargs)
        end_process_time = time.time()
        processing_time = end_process_time - start_process_time
        
        status_lines.append("原始识别结果获取完毕, 正在格式化...")
        
        result_label = f"识别结果 (耗时: {processing_time:.2f} 秒)"
        
        # 普通模式
        if output_mode == "normal":
            texts = [r.get('text', '').strip() for r in res if r.get('text')]
            result_text = "\n".join(texts) if texts else "识别结果为空。"
        else:
            # 时间戳模式，构建 SRT 样式
            segments = []
            if res:
                raw = res[0].get('text', '')
                timestamps = res[0].get('timestamp', [])
                
                # 情感和事件标签
                emo_tags = {'<|HAPPY|>', '<|SAD|>', '<|ANGRY|>', '<|NEUTRAL|>', '<|FEARFUL|>', '<|DISGUSTED|>', '<|SURPRISED|>'}
                event_tags = {'<|Speech|>', '<|BGM|>', '<|Applause|>', '<|Laughter|>', '<|Cry|>', '<|Sneeze|>', '<|Breath|>', '<|Cough|>', '<|Event_UNK|>'}
                
                parts = raw.split('<|zh|>')
                offset = 0
                for part in parts:
                    if not part:
                        continue
                    m = re.match(r'((?:<\|[^|]+\|>)+)', part)
                    if m:
                        tags = ''.join([t for t in re.findall(r'<\|[^|]+\|>', m.group(1)) if t in emo_tags or t in event_tags])
                        content = part[len(m.group(1)):].strip()
                    else:
                        tags = ''
                        content = part.strip()
                    
                    length = len(content)
                    if length == 0:
                        continue
                    
                    st = timestamps[offset][0] if offset < len(timestamps) else 0
                    et = timestamps[offset + length - 1][1] if offset + length - 1 < len(timestamps) else st
                    segments.append((tags + content, st, et))
                    offset += length
            
            # 格式化输出
            out_lines = []
            for idx, (txt, st, et) in enumerate(segments, 1):
                out_lines.append(str(idx))
                out_lines.append(f"{ms2srt(st)} --> {ms2srt(et)}")
                out_lines.append(txt)
                out_lines.append("")
            
            result_text = "\n".join(out_lines) if out_lines else "识别结果为空。"
        
        status_lines.append(f"识别完成，耗时: {processing_time:.2f} 秒")
        status_info = "\n".join(status_lines)
        
        return result_text, status_info, gr.update(label=result_label)
        
    except Exception as e:
        error_info = f"识别过程中发生错误: {str(e)}\n请检查模型路径、音频文件格式和依赖项是否正确安装。\n错误详情: {type(e).__name__}: {e}"
        status_lines.append("--- 错误 ---")
        status_lines.append(error_info)
        status_info = "\n".join(status_lines)
        
        return "", status_info, gr.update(label="识别结果")

# Gradio 界面
with gr.Blocks(theme=gr.themes.Soft(), title="SenseVoice 语音识别客户端") as demo:
    gr.Markdown("# SenseVoice 语音识别 (带时间戳和情感识别)")
    gr.Markdown("上传音频文件，选择运行设备和模型进行识别。支持情感标签识别、事件检测和多种输出格式。")
    
    with gr.Row():
        with gr.Column(scale=1):
            # 音频文件上传
            audio_input = gr.Audio(type="filepath", label="上传音频文件")
            
            with gr.Row():
                # 设备选择
                device_select = gr.Radio(
                    choices=["cuda", "cpu"], 
                    value=default_device, 
                    label="选择设备"
                )
                
                # 输出模式选择
                mode_select = gr.Dropdown(
                    choices=[("时间戳格式 (SRT)", "timestamp"), ("普通文本", "normal")], 
                    value="timestamp", 
                    label="输出格式"
                )
            
            # 模型配置折叠面板
            with gr.Accordion("模型配置", open=False):
                asr_select = gr.Dropdown(
                    choices=list(asr_models.keys()), 
                    value=default_asr, 
                    label="ASR 模型"
                )
                vad_select = gr.Dropdown(
                    choices=list(vad_models_with_none.keys()), 
                    value=default_vad,
                    label="VAD 模型"
                )
            
            # 状态信息显示
            info_text = gr.Textbox(
                label="状态信息", 
                value=f"当前设备: {default_device.upper()}, 支持情感标签识别、事件检测和多种输出格式", 
                interactive=False,
                lines=3
            )
            
            # 识别按钮
            submit_button = gr.Button("开始识别", variant="primary", size="lg")
        
        with gr.Column(scale=2):
            # 识别结果显示
            text_output = gr.Textbox(
                label="识别结果", 
                lines=50, 
                interactive=False,
                placeholder="识别结果将显示在这里..."
            )
    
    # 事件绑定
    submit_button.click(
        fn=process_audio,
        inputs=[audio_input, device_select, asr_select, vad_select, mode_select],
        outputs=[text_output, info_text, text_output]
    )

if __name__ == '__main__':
    demo.launch(inbrowser=True, server_port=7861)
