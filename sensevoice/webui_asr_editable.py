import gradio as gr
import os
import sys
import configparser
import re
import time
import funasr
import pandas as pd
from pathlib import Path

# --- Global Variables ---
MODEL_CONFIG_FILE = "model.conf"  # 使用与GUI版本相同的配置文件名称
model_cache = {}

# 获取当前脚本目录
current_script_dir = Path(os.path.dirname(os.path.abspath(__file__)))

# 可能的配置文件位置列表
possible_config_paths = [
    current_script_dir / MODEL_CONFIG_FILE,  # 脚本同目录
    Path(os.getcwd()) / MODEL_CONFIG_FILE,  # 当前工作目录
    Path("/home/<USER>/WorkSpace/LightASR") / MODEL_CONFIG_FILE,  # 项目根目录
    current_script_dir.parent / MODEL_CONFIG_FILE,  # 父目录
]

# 检查所有可能的路径
for path in possible_config_paths:
    if path.exists():
        model_config_path = path
        print(f"找到配置文件: {model_config_path}")
        break
else:
    # 如果循环正常结束但没有break，表示没有找到文件
    model_config_path = current_script_dir / MODEL_CONFIG_FILE  # 默认路径


# --- Helper Functions ---

def _ms2srt(ms):
    """Convert milliseconds to SRT time format."""
    s = ms / 1000
    m = int(s // 60)
    s = s % 60
    h = int(m // 60)
    m = m % 60
    return f"{h:02d}:{m:02d}:{s:06.3f}".replace('.', ',')

def load_model_configs():
    """加载ASR和VAD模型配置，与GUI版本保持一致"""
    asr_models = {}
    vad_models = {}
    
    if not model_config_path.exists():
        print(f"警告: {MODEL_CONFIG_FILE} 未在 {model_config_path} 找到。模型下拉列表可能为空。")
        return {"choices": ["无模型"], "paths": {}}, {"choices": ["None (不使用)"], "paths": {}}

    config = configparser.ConfigParser()
    try:
        config.read(model_config_path, encoding='utf-8')
        
        # 读取ASR模型配置 (使用asr_models_dir段落)
        if 'asr_models_dir' in config:
            for name, path in config.items('asr_models_dir'):
                asr_models[name] = path
        else:
            print(f"警告: 配置文件中未找到 [asr_models_dir] 部分。")
        
        # 读取VAD模型配置 (使用vad_models_dir段落)
        if 'vad_models_dir' in config:
            for name, path in config.items('vad_models_dir'):
                vad_models[name] = path
        else:
            print(f"警告: 配置文件中未找到 [vad_models_dir] 部分。")
            
    except Exception as e:
        print(f"加载模型配置时出错: {e}")
        return {"choices": ["加载错误"], "paths": {}}, {"choices": ["加载错误"], "paths": {}}

    # 准备下拉列表选项
    asr_choices = list(asr_models.keys())
    if not asr_choices:
        asr_choices = ["无模型"]
    
    # 为VAD模型添加"不使用"选项
    vad_choices = ["None (不使用)"] + list(vad_models.keys()) if vad_models else ["None (不使用)"]
    vad_models["None (不使用)"] = None
    
    return {"choices": asr_choices, "paths": asr_models}, \
           {"choices": vad_choices, "paths": vad_models}

# Load models at startup to populate dropdowns
asr_config, vad_config = load_model_configs()

def format_results_for_table_and_srt(rec_result_list, emotion_recognition_enabled):
    """
    Formats FunASR results into table data and SRT string.
    Handles results with or without timestamps, and with or without emotion/event tags.
    """
    table_data = []
    srt_lines = []
    segment_idx = 0

    if not rec_result_list or not rec_result_list[0]:
        return [], "No recognition result."

    for rec_result_item_idx, rec_result_item in enumerate(rec_result_list):
        if not isinstance(rec_result_item, dict): # Ensure item is a dict
            print(f"Warning: rec_result_item {rec_result_item_idx} is not a dict: {rec_result_item}")
            continue

        raw_text = rec_result_item.get('text', '')
        timestamps = rec_result_item.get('timestamp', []) # List of [start_ms, end_ms] for each char/word

        # If no timestamps, treat the whole text as one segment
        if not timestamps:
            segment_idx += 1
            text_content = raw_text.strip()
            if text_content: # Only add if there's actual text
                table_data.append([
                    segment_idx, "N/A", "N/A", # No., Start, End
                    "N/A", "N/A", # Emotion, Event
                    text_content
                ])
                srt_lines.append(str(segment_idx))
                srt_lines.append(f"00:00:00,000 --> 00:00:00,000") # Placeholder timestamp
                srt_lines.append(text_content)
                srt_lines.append("")
            continue # Move to next item in rec_result_list

        # Logic for handling text with timestamps (similar to original RecognitionWorker)
        emo_tags_set = {'<|HAPPY|>', '<|SAD|>', '<|ANGRY|>', '<|NEUTRAL|>', '<|FEARFUL|>', '<|DISGUSTED|>', '<|SURPRISED|>'}
        event_tags_set = {'<|Speech|>', '<|BGM|>', '<|Applause|>', '<|Laughter|>', '<|Cry|>', '<|Sneeze|>', '<|Breath|>', '<|Cough|>', '<|Event_UNK|>'}
        
        # Split by language tag if present, otherwise process whole text
        # FunASR paraformer-large-context-vad-zh output might have <|zh|> tags
        parts = re.split(r'(<\|zh\|>|<\|en\|>|<\|ja\|>|<\|ko\|>)', raw_text) # Keep delimiters
        
        current_char_offset = 0 # Tracks character position in the original raw_text for timestamp mapping

        processed_parts = []
        current_part_text = ""
        for p_idx, p_text in enumerate(parts):
            if p_text.startswith("<|") and p_text.endswith("|>") and len(p_text) > 4 : # is a language tag
                if current_part_text: # process accumulated text before language tag
                    processed_parts.append(current_part_text)
                    current_part_text = ""
                processed_parts.append(p_text) # add language tag itself
            else:
                current_part_text += p_text
        if current_part_text:
            processed_parts.append(current_part_text)


        for part_text_segment in processed_parts:
            if not part_text_segment or part_text_segment.isspace():
                current_char_offset += len(part_text_segment)
                continue
            
            # Skip language tags like <|zh|> for actual content processing
            if part_text_segment.startswith("<|") and part_text_segment.endswith("|>") and len(part_text_segment) > 4 and part_text_segment[2:-2].lower() in ["zh", "en", "ja", "ko"]:
                current_char_offset += len(part_text_segment)
                continue

            # Attempt to parse tags and content within this segment
            # This regex tries to find all tags at the beginning of the segment
            tag_match = re.match(r'((?:<\|[^|]+\|>)+)', part_text_segment)
            
            extracted_emotion_tags = []
            extracted_event_tags = []
            content_text = part_text_segment
            segment_prefix_len = 0

            if tag_match:
                all_tags_in_segment = re.findall(r'<\|[^|]+\|>', tag_match.group(1))
                if emotion_recognition_enabled:
                    extracted_emotion_tags = [t for t in all_tags_in_segment if t in emo_tags_set]
                extracted_event_tags = [t for t in all_tags_in_segment if t in event_tags_set]
                content_text = part_text_segment[len(tag_match.group(1)):].strip()
                segment_prefix_len = len(tag_match.group(1))
            else: # No tags at the beginning
                content_text = part_text_segment.strip()

            if not content_text: # If only tags or empty after stripping
                current_char_offset += len(part_text_segment)
                continue

            # Determine start and end times for this content_text segment
            # This requires careful mapping of content_text back to timestamps
            # The original GUI's logic for `offset` and `length` was based on `raw.split('<|zh|>')`
            # Here, we need a more robust way if the structure is different.
            # Assuming timestamps correspond to characters in the *original* raw_text.
            
            # Simplified approach: if content_text is a substring of raw_text, find its start
            # This is tricky because tags might be interspersed.
            # For now, let's assume timestamps are somewhat aligned with characters of content_text
            # after tags are conceptually "removed".
            
            # A more robust way would be if FunASR provided per-segment timestamps directly.
            # Given the `timestamps` list is per character of `raw_text`:
            
            # Heuristic: find the start of content_text in part_text_segment (after its own prefix tags)
            # and map that to the global current_char_offset
            
            # The number of characters in content_text
            content_len = len(content_text)
            if content_len == 0:
                current_char_offset += len(part_text_segment)
                continue

            # Determine start and end time from the global timestamps list
            # current_char_offset should point to the start of part_text_segment in raw_text
            # segment_prefix_len is the length of tags *within* part_text_segment
            
            start_char_idx_in_raw = current_char_offset + segment_prefix_len
            end_char_idx_in_raw = start_char_idx_in_raw + content_len -1

            if start_char_idx_in_raw < len(timestamps) and end_char_idx_in_raw < len(timestamps):
                st_ms = timestamps[start_char_idx_in_raw][0]
                et_ms = timestamps[end_char_idx_in_raw][1]
            elif content_len > 0 and timestamps: # Fallback if precise mapping fails but timestamps exist
                st_ms = timestamps[0][0] # Use first available timestamp
                et_ms = timestamps[-1][1] # Use last available timestamp
                print(f"Warning: Timestamp mapping issue for segment '{content_text}'. Using broad timestamps.")
            else: # No timestamps or mapping failed completely
                st_ms, et_ms = 0, 0 # Default to 0 if no timestamp info

            current_char_offset += len(part_text_segment) # Advance global offset by the length of the processed part_text_segment

            segment_idx += 1
            emotion_str = "".join(extracted_emotion_tags) if extracted_emotion_tags else "N/A"
            event_str = "".join(extracted_event_tags) if extracted_event_tags else "N/A"

            table_data.append([
                segment_idx,
                _ms2srt(st_ms) if st_ms > 0 or et_ms > 0 else "N/A",
                _ms2srt(et_ms) if st_ms > 0 or et_ms > 0 else "N/A",
                emotion_str,
                event_str,
                content_text
            ])

            srt_lines.append(str(segment_idx))
            srt_lines.append(f"{_ms2srt(st_ms)} --> {_ms2srt(et_ms)}")
            full_text_for_srt = (emotion_str if emotion_str != "N/A" else "") + \
                                (event_str if event_str != "N/A" else "") + \
                                content_text
            srt_lines.append(full_text_for_srt)
            srt_lines.append("")

    return table_data, "\n".join(srt_lines)


def recognize_audio(audio_file_path, device, asr_model_name, vad_model_name, 
                    language, hotwords, emotion_enabled, progress=gr.Progress()):
    """
    主要音频识别函数
    """
    if not audio_file_path:
        return "请先上传音频文件。", [], "", "0.00 s", [], "", ""

    progress(0, desc="开始处理...")

    try:
        # 获取模型路径
        asr_model_path = asr_config["paths"].get(asr_model_name)
        vad_model_path = vad_config["paths"].get(vad_model_name)

        if not asr_model_path or "无模型" in asr_model_name or "加载错误" in asr_model_name:
            return "请选择有效的 ASR 模型。", [], "", "0.00 s", [], "", ""

        progress(0.1, desc="加载模型...")

        # 模型缓存（与GUI版本相同的方式）
        model_key = f"{device}_{asr_model_path}_{str(vad_model_path)}"
        
        if model_key in model_cache:
            model = model_cache[model_key]
            progress(0.2, desc="使用缓存模型...")
        else:
            progress(0.15, desc="首次加载模型 (可能需要一些时间)...")
            
            # 构建模型参数（参考原始GUI实现）
            model_kwargs = {
                "disable_update": True,
                "device": device,
                "model": asr_model_path
            }
            
            # 添加VAD模型（如果选择了）
            if vad_model_path:
                model_kwargs["vad_model"] = vad_model_path
                
            # 创建模型
            model = funasr.AutoModel(**model_kwargs)
            # 缓存模型以供后续使用
            model_cache[model_key] = model
            progress(0.3, desc="模型加载完毕.")

        
        generate_kwargs = {
            "language": language if language != "auto" else None, # FunASR expects None for auto
            "hotword": hotwords if hotwords else "",
            "use_timestamp": True, # Always try to get timestamps
        }
        if model.model_type == funasr.constants.ModelType.Paraformer: # Only Paraformer supports emotion
             generate_kwargs["emotion_recognition"] = emotion_enabled


        progress(0.5, desc="开始识别...")
        start_time = time.time()
        
        # rec_result = model.generate(input=audio_file_path, **generate_kwargs)
        # The input can be a string (path) or a NumPy array.
        # For long audio, it's better to use streaming or chunking if the model supports it.
        # The original GUI uses generate, which implies it handles the whole file.
        
        # FunASR AutoModel can take a file path directly
        rec_result_list = model.generate(input=audio_file_path, **generate_kwargs)

        end_time = time.time()
        processing_time = end_time - start_time
        progress(0.9, desc="识别完成, 格式化结果...")

        if not rec_result_list:
            return "识别结果为空。", [], "", f"{processing_time:.2f} s", [], ""

        table_data, srt_content = format_results_for_table_and_srt(rec_result_list, emotion_enabled)
        
        txt_content_lines = [row[5] for row in table_data if len(row) > 5] # Assuming text is the 6th element
        txt_content = "\n".join(txt_content_lines)

        progress(1, desc="处理完毕!")
        
        return (
            "处理完毕!", 
            pd.DataFrame(table_data, columns=["编号", "开始时间", "结束时间", "情感标签", "事件标签", "识别文本"]), 
            srt_content, 
            f"{processing_time:.2f} s",
            table_data, # Original data for reset
            srt_content, # For download state
            txt_content  # For download state
        )

    except Exception as e:
        error_message = f"发生错误: {str(e)}\n类型: {type(e).__name__}"
        import traceback
        traceback.print_exc() # Print to console for more details
        return error_message, [], "", "0.00 s", [], "", ""

def generate_download_file(content, extension, prefix="result"):
    """Generates a temporary file with content and returns its path."""
    if content is None:
        # Create an empty file if content is None to avoid errors with gr.File
        content = ""
    
    temp_dir = Path(gr.utils.get_temp_dir()) / "asr_results"
    temp_dir.mkdir(parents=True, exist_ok=True)
    
    timestamp = time.strftime("%Y%m%d-%H%M%S")
    filename = f"{prefix}_{timestamp}.{extension}"
    filepath = temp_dir / filename
    
    try:
        with open(filepath, "w", encoding="utf-8") as f:
            f.write(content)
        return filepath
    except Exception as e:
        print(f"Error writing download file: {e}")
        # Fallback: create an empty file with error message
        error_content = f"Error generating file: {e}"
        error_filename = f"error_{timestamp}.txt"
        error_filepath = temp_dir / error_filename
        with open(error_filepath, "w", encoding="utf-8") as f:
            f.write(error_content)
        return error_filepath


def update_srt_txt_from_table(edited_table_data):
    """Updates SRT and TXT content based on edited table data."""
    if edited_table_data is None or edited_table_data.empty:
        return "", ""

    new_srt_lines = []
    new_txt_lines = []
    df = edited_table_data # Gradio passes DataFrame directly

    for index, row in df.iterrows():
        try:
            idx = row.get("编号", index + 1)
            st = row.get("开始时间", "00:00:00,000")
            et = row.get("结束时间", "00:00:00,000")
            emotion = row.get("情感标签", "")
            event = row.get("事件标签", "")
            text = str(row.get("识别文本", "")) # Ensure text is string

            new_txt_lines.append(text)

            new_srt_lines.append(str(idx))
            new_srt_lines.append(f"{st} --> {et}")
            
            full_text_for_srt = (emotion if pd.notna(emotion) and emotion != "N/A" else "") + \
                                (event if pd.notna(event) and event != "N/A" else "") + \
                                text
            new_srt_lines.append(full_text_for_srt)
            new_srt_lines.append("")
        except Exception as e:
            print(f"Error processing row {index} for SRT/TXT update: {e}, row data: {row}")
            # Append a placeholder or skip if critical data is missing
            new_txt_lines.append(f"[Error processing row {index}]")
            new_srt_lines.append(str(idx if 'idx' in locals() else index + 1))
            new_srt_lines.append("00:00:00,000 --> 00:00:00,000")
            new_srt_lines.append(f"[Error processing row {index}]")
            new_srt_lines.append("")


    return "\n".join(new_srt_lines), "\n".join(new_txt_lines)


# --- Gradio UI Definition ---
with gr.Blocks(theme=gr.themes.Soft()) as demo:
    gr.Markdown("# SenseVoice 语音识别 WebUI (可编辑)")
    # 计算ASR和VAD模型的实际数量
    asr_model_count = len(asr_config['choices']) if '无模型' not in asr_config['choices'] else 0
    if '加载错误' in asr_config['choices']:
        asr_model_count = 0
        
    # VAD模型数量需要减去"None (不使用)"选项
    vad_model_count = len(vad_config['choices']) - 1 if len(vad_config['choices']) > 1 else 0
    if '加载错误' in vad_config['choices']:
        vad_model_count = 0
        
    gr.Markdown(f"模型配置文件: `{model_config_path}` (ASR: {asr_model_count} 个模型, VAD: {vad_model_count} 个模型)")


    # Store original table data and current downloadable content
    original_table_data_state = gr.State([]) 
    current_srt_content_state = gr.State("")
    current_txt_content_state = gr.State("")

    with gr.Row():
        with gr.Column(scale=1):
            gr.Markdown("### 1. 上传与配置")
            audio_input = gr.Audio(type="filepath", label="上传音频文件", sources=["upload", "microphone"])
            
            device_select = gr.Radio(["cpu", "cuda"], label="运行设备", value="cpu", info="选择 'cuda' 如果您有NVIDIA GPU并已配置好环境。")
            
            asr_model_select = gr.Dropdown(choices=asr_config["choices"], label="ASR 模型", value=asr_config["choices"][0], info="选择语音识别模型。")
            vad_model_select = gr.Dropdown(choices=vad_config["choices"], label="VAD 模型 (可选)", value=vad_config["choices"][0], info="选择语音活动检测模型，有助于处理长音频或静音片段。")
            
            language_select = gr.Dropdown(["auto", "zh", "en", "ja", "ko"], label="识别语言", value="auto", info="选择音频的语言，'auto'为自动检测。")
            hotword_input = gr.Textbox(label="热词 (可选)", placeholder="例如: 阿里巴巴 达摩院 SenseVoice (用空格分隔)", info="提供热词可以提高特定词汇的识别准确率。")
            emotion_checkbox = gr.Checkbox(label="开启情感识别 (部分模型支持)", value=True, info="如果模型支持，将识别并标注情感标签。")
            
            recognize_button = gr.Button("▶️ 开始识别", variant="primary")
            
            status_text = gr.Textbox(label="处理状态", interactive=False)
            processing_time_text = gr.Textbox(label="处理耗时", interactive=False)

        with gr.Column(scale=2):
            gr.Markdown("### 2. 识别结果与编辑")
            # Tab for Table and SRT view
            with gr.Tabs():
                with gr.TabItem("表格结果 (可编辑)"):
                    result_table = gr.DataFrame(
                        headers=["编号", "开始时间", "结束时间", "情感标签", "事件标签", "识别文本"], 
                        label="识别分段结果", 
                        interactive=True, # 允许编辑
                        col_count=(6, "fixed"),
                        wrap=True
                        # 移除height参数，因为当前版本不支持
                    )
                    reset_button = gr.Button("🔄 重置编辑到原始结果")
                with gr.TabItem("SRT 格式"):
                    srt_output_display = gr.Textbox(label="SRT 格式结果 (根据表格实时更新)", lines=15, interactive=False)
            
            gr.Markdown("### 3. 下载结果")
            with gr.Row():
                download_txt_button = gr.Button("⬇️ 下载 TXT")
                download_srt_button = gr.Button("⬇️ 下载 SRT")
            
            download_file_output = gr.File(label="下载文件", interactive=False) # Used as a target for downloads

    # --- Event Handlers ---
    recognize_button.click(
        fn=recognize_audio,
        inputs=[audio_input, device_select, asr_model_select, vad_model_select, language_select, hotword_input, emotion_checkbox],
        outputs=[status_text, result_table, srt_output_display, processing_time_text, 
                 original_table_data_state, current_srt_content_state, current_txt_content_state]
    )

    def table_edited_handler(edited_df_data):
        # This function will be called when the DataFrame is edited.
        # edited_df_data is the new state of the DataFrame.
        # We need to regenerate SRT and TXT from this.
        new_srt, new_txt = update_srt_txt_from_table(edited_df_data)
        return new_srt, new_txt, new_srt # Update display, and state for download

    # When the table is edited by the user
    result_table.change( 
        fn=table_edited_handler,
        inputs=[result_table],
        outputs=[srt_output_display, current_txt_content_state, current_srt_content_state]
    )
    
    # Reset button functionality
    def reset_table_data(original_data_list_of_lists):
        if not original_data_list_of_lists: # If empty list (e.g. no recognition yet)
            return pd.DataFrame(columns=["编号", "开始时间", "结束时间", "情感标签", "事件标签", "识别文本"]) # Return empty DataFrame
        
        # Convert list of lists back to DataFrame
        df = pd.DataFrame(original_data_list_of_lists, columns=["编号", "开始时间", "结束时间", "情感标签", "事件标签", "识别文本"])
        # Also update the SRT and TXT outputs based on this reset data
        new_srt, new_txt = update_srt_txt_from_table(df)
        return df, new_srt, new_txt, new_srt # df for table, srt for display, txt for state, srt for state

    reset_button.click(
        fn=reset_table_data,
        inputs=[original_table_data_state],
        outputs=[result_table, srt_output_display, current_txt_content_state, current_srt_content_state]
    )

    # Download handlers
    download_txt_button.click(
        fn=lambda txt_content: generate_download_file(txt_content, "txt", "asr_result"),
        inputs=[current_txt_content_state],
        outputs=[download_file_output]
    )
    download_srt_button.click(
        fn=lambda srt_content: generate_download_file(srt_content, "srt", "asr_result"),
        inputs=[current_srt_content_state],
        outputs=[download_file_output]
    )

if __name__ == "__main__":
    # To find model.ini, ensure the script is run from its directory or provide an absolute path
    print(f"Attempting to load model.ini from: {model_config_path}")
    if not model_config_path.exists():
        print(f"CRITICAL: {MODEL_CONFIG_FILE} not found. Please ensure it exists in the script's directory: {current_script_dir}")
    
    demo.launch()
