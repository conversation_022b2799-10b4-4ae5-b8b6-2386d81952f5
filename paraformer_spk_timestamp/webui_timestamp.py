import gradio as gr
from funasr import AutoModel
import os
import configparser
import time

# 辅助函数，参考自 demo_timestamp.py
def format_timestamp(ms):
    """将毫秒转换为 HH:MM:SS,ms 格式"""
    seconds = int(ms / 1000)
    milliseconds = int(ms % 1000)
    hours = seconds // 3600
    seconds %= 3600
    minutes = seconds // 60
    seconds %= 60
    return f"{hours:02d}:{minutes:02d}:{seconds:02d},{milliseconds:03d}"

# --- 从 model.conf 加载模型配置 ---
config = configparser.ConfigParser()
config_paths = [
    os.path.join(os.path.dirname(os.path.abspath(__file__)), 'model.conf'),
    # os.path.join(os.path.dirname(os.path.abspath(__file__)), 'model_mac.conf')
]

loaded_config_path = None
for config_path in config_paths:
    if os.path.exists(config_path):
        try:
            config.read(config_path, encoding='utf-8')
            loaded_config_path = config_path
            break
        except Exception as e:
            print(f"读取配置文件 {config_path} 失败: {e}")

if not loaded_config_path:
    raise FileNotFoundError(f"配置文件未找到，检查路径: {config_paths}")

print(f"成功加载配置文件: {loaded_config_path}")

# 获取各种模型列表
asr_models = dict(config['asr_models_dir']) if 'asr_models_dir' in config else {}
asr_seaco_models = dict(config['asr_seaco_models_dir']) if 'asr_seaco_models_dir' in config else {}
vad_models = dict(config['vad_models_dir']) if 'vad_models_dir' in config else {}
punc_models = dict(config['punc_models_dir']) if 'punc_models_dir' in config else {}
spk_models = dict(config['spk_models_dir']) if 'spk_models_dir' in config else {}

# 默认模型配置
default_asr_long = list(asr_models.keys())[0] if asr_models else None
default_asr_seaco = list(asr_seaco_models.keys())[0] if asr_seaco_models else None
default_vad = list(vad_models.keys())[0] if vad_models else None
default_punc = list(punc_models.keys())[0] if punc_models else None
default_spk = list(spk_models.keys())[0] if spk_models else None

# --- 全局模型缓存 ---
model_cache = {}

def get_model(device, model_type, asr_model_name, vad_model_name, punc_model_name, spk_model_name):
    """获取或加载所选模型实例"""
    key = f"{device}_{model_type}_{asr_model_name}_{vad_model_name}_{punc_model_name}_{spk_model_name}"
    if key not in model_cache:
        print(f"为配置 {key} 加载模型...")
        
        # 根据模型类型选择ASR模型路径
        if model_type == "seaco":
            asr_path = asr_seaco_models.get(asr_model_name)
        else:
            asr_path = asr_models.get(asr_model_name)
        
        vad_path = vad_models.get(vad_model_name)
        punc_path = punc_models.get(punc_model_name)
        spk_path = spk_models.get(spk_model_name)
        
        if not all([asr_path, vad_path, punc_path, spk_path]):
            missing = []
            if not asr_path: missing.append(f"ASR: {asr_model_name}")
            if not vad_path: missing.append(f"VAD: {vad_model_name}")
            if not punc_path: missing.append(f"PUNC: {punc_model_name}")
            if not spk_path: missing.append(f"SPK: {spk_model_name}")
            raise ValueError(f"配置 {key} 的模型路径缺失: {', '.join(missing)}")
        
        params = {
            "disable_update": True,
            "device": device,
            "model": asr_path,
            "vad_model": vad_path,
            "punc_model": punc_path,
            "spk_model": spk_path,
            "timestamp": True,
        }
        
        # 为Seaco模型添加VAD参数
        if model_type == "seaco":
            params["vad_kwargs"] = {"max_single_segment_time": 60000}
        
        model_cache[key] = AutoModel(**params)
        print(f"配置 {key} 的模型加载完成。")
    return model_cache[key]

def update_asr_models(model_type):
    """根据模型类型更新ASR模型选择"""
    if model_type == "长音频模型":
        choices = list(asr_models.keys())
        default = default_asr_long
        info = "已切换到长音频模型，支持数小时音频处理"
    else:  # 热词增强模型
        choices = list(asr_seaco_models.keys())
        default = default_asr_seaco
        info = "已切换到热词增强模型，支持自定义热词提高识别准确度"
    
    return gr.update(choices=choices, value=default), info

def update_hotword_input(model_type):
    """根据模型类型启用/禁用热词输入"""
    if model_type == "热词增强模型":
        return gr.update(interactive=True, placeholder="请输入热词，使用空格分隔")
    else:
        return gr.update(interactive=False, value="", placeholder="长音频模型不支持热词功能")

def update_speaker_input(speaker_enabled):
    """根据说话人启用状态显示/隐藏说话人数输入"""
    return gr.update(visible=speaker_enabled)

# --- Gradio 的主要处理函数 ---
def process_audio(audio_file_path, device, model_type, asr_choice, vad_choice, punc_choice, spk_choice, hotword, output_mode, speaker_enabled, preset_spk_num):
    if not audio_file_path:
        return "请先上传一个音频文件。"
    if not all([asr_choice, vad_choice, punc_choice, spk_choice]):
        return "错误：请确保所有模型（ASR, VAD, PUNC, SPK）都已选择。"

    spk_num_arg = None
    if speaker_enabled and preset_spk_num is not None and int(preset_spk_num) > 0:
        spk_num_arg = int(preset_spk_num)
        print(f"指定说话人数量: {spk_num_arg}")
    elif speaker_enabled:
        print("启用说话人识别，自动检测说话人数量。")
    else:
        print("说话人识别已禁用。")

    # 确定模型类型标识
    model_type_key = "seaco" if model_type == "热词增强模型" else "long"
    
    print(f"接收到文件: {audio_file_path}, 设备: {device}, 模型类型: {model_type}")
    print(f"ASR={asr_choice}, VAD={vad_choice}, PUNC={punc_choice}, SPK={spk_choice}")
    if hotword and model_type == "热词增强模型":
        print(f"热词={hotword}")
    print(f"输出模式={output_mode}")
    
    try:
        model_config_id = f"{device}_{model_type_key}_{asr_choice}_{vad_choice}_{punc_choice}_{spk_choice}"
        if not hasattr(process_audio, 'current_model') or process_audio.current_model != model_config_id:
            print(f"加载新模型组合: {model_config_id}")
            process_audio.model = get_model(device, model_type_key, asr_choice, vad_choice, punc_choice, spk_choice)
            process_audio.current_model = model_config_id
        model = process_audio.model
        
        print(f"开始处理音频文件: {audio_file_path}")
        gen_kwargs = {"input": audio_file_path, "cache": {}, "return_raw_text": True}
        if spk_num_arg:
            gen_kwargs["preset_spk_num"] = spk_num_arg
        if hotword and hotword.strip() and model_type == "热词增强模型":
            gen_kwargs["hotword"] = hotword
        
        # 记录模型识别开始时间
        start_process_time = time.time()
        res = model.generate(**gen_kwargs)
        # 记录识别完成时间
        end_process_time = time.time()
        processing_time = end_process_time - start_process_time
        print(f"模型识别耗时: {processing_time:.2f} 秒")
        print("识别完成，开始格式化结果...")

        formatted = []
        idx = 1
        for result in res:
            if 'sentence_info' in result:
                for sent in result['sentence_info']:
                    spk = sent.get('spk')
                    # 只有在启用说话人识别时才显示说话人标签
                    if speaker_enabled and isinstance(spk, int):
                        spk_fmt = f"[spk{spk+1}]   "
                    else:
                        spk_fmt = ""
                        
                    text = sent.get('text','').rstrip(',.。，!！?？')
                    st = format_timestamp(sent.get('start',0))
                    et = format_timestamp(sent.get('end',0))
                    if output_mode == 'timestamp':
                        formatted.append(str(idx)); formatted.append(f"{st} --> {et}"); formatted.append(f"{spk_fmt}{text}"); formatted.append(""); idx += 1
                    else:  # normal
                        formatted.append(f"{spk_fmt}{text}")
            elif 'raw_text' in result and 'timestamp' in result:
                spk = result.get('spk')
                if speaker_enabled and isinstance(spk, int):
                    spk_fmt = f"[spk{spk+1}]   "
                else:
                    spk_fmt = ""
                    
                pieces = result.get('raw_text','').split()
                for (s_ms,e_ms), piece in zip(result.get('timestamp',[]), pieces):
                    st = format_timestamp(s_ms); et = format_timestamp(e_ms)
                    txt = piece.rstrip(',.。，!！?？')
                    if output_mode == 'timestamp':
                        formatted.append(str(idx)); formatted.append(f"{st} --> {et}"); formatted.append(f"{spk_fmt}{txt}"); formatted.append(""); idx += 1
                    else:  # normal
                        formatted.append(f"{spk_fmt}{piece}")
            elif 'timestamp' in result:
                spk = result.get('spk')
                if speaker_enabled and isinstance(spk, int):
                    spk_fmt = f"[spk{spk+1}]   "
                else:
                    spk_fmt = ""
                    
                text = result.get('text','').rstrip(',.。，!！?？')
                if result.get('timestamp'):
                    start_ms = result['timestamp'][0][0]
                    end_ms = result['timestamp'][-1][1]
                    st = format_timestamp(start_ms); et = format_timestamp(end_ms)
                    if output_mode == 'timestamp':
                        formatted.append(str(idx)); formatted.append(f"{st} --> {et}"); formatted.append(f"{spk_fmt}{text}"); formatted.append(""); idx += 1
                    else:
                        formatted.append(f"{spk_fmt}{text}")
            else:
                txt = result.get('text','').rstrip(',.。，!！?？')
                formatted.append(txt)
        
        output = "\n".join(formatted)
        print("结果格式化完成。")
        # 更新 Textbox 标签显示耗时
        label = f"识别结果(耗时{processing_time:.2f}秒)"
        return output, gr.update(label=label)
    except Exception as e:
        print(f"处理音频时出错: {e}")
        import traceback; traceback.print_exc()
        return f"处理失败: {e}", gr.update(label="识别结果")

# --- Gradio 界面定义 ---
with gr.Blocks(theme=gr.themes.Soft()) as demo:
    gr.Markdown("# Paraformer 统一语音识别")
    gr.Markdown("支持长音频识别和热词增强，带时间戳、说话人分离。上传音频文件，选择模型类型、设备及参数进行识别。")
    
    with gr.Row():
        with gr.Column(scale=1):
            audio_input = gr.Audio(type="filepath", label="上传音频文件")
            
            with gr.Row():
                device_select = gr.Radio(choices=["cuda","cpu"], value="cpu", label="选择设备")
                model_type_select = gr.Radio(
                    choices=["长音频模型", "热词增强模型"], 
                    value="长音频模型", 
                    label="模型类型"
                )
            
            with gr.Row():
                mode_select = gr.Dropdown(choices=["normal","timestamp"], value="timestamp", label="输出格式")
                speaker_enable_checkbox = gr.Checkbox(label="启用说话人识别", value=True)
            
            preset_spk_num_input = gr.Number(label="指定说话人数 (0=自动)", value=0, minimum=0, step=1, visible=True)
            
            hotword_input = gr.Textbox(
                label="热词 (空格分隔)", 
                placeholder="长音频模型不支持热词功能",
                interactive=False
            )
            
            with gr.Accordion("模型配置", open=False):
                asr_select = gr.Dropdown(
                    choices=list(asr_models.keys()), 
                    value=default_asr_long, 
                    label="ASR 模型"
                )
                vad_select = gr.Dropdown(choices=list(vad_models.keys()), value=default_vad, label="VAD 模型")
                punc_select = gr.Dropdown(choices=list(punc_models.keys()), value=default_punc, label="标点模型")
                spk_select = gr.Dropdown(choices=list(spk_models.keys()), value=default_spk, label="说话人模型")
            
            info_text = gr.Textbox(label="状态信息", value="当前使用长音频模型，支持数小时音频处理", interactive=False)
            submit_button = gr.Button("开始识别", variant="primary")
        
        with gr.Column(scale=2):
            text_output = gr.Textbox(label="识别结果", lines=50, interactive=False)
    
    # 事件绑定
    model_type_select.change(
        fn=update_asr_models,
        inputs=[model_type_select],
        outputs=[asr_select, info_text]
    )
    
    model_type_select.change(
        fn=update_hotword_input,
        inputs=[model_type_select],
        outputs=[hotword_input]
    )
    
    speaker_enable_checkbox.change(
        fn=update_speaker_input,
        inputs=[speaker_enable_checkbox],
        outputs=[preset_spk_num_input]
    )
    
    submit_button.click(
        fn=process_audio,
        inputs=[
            audio_input, device_select, model_type_select, asr_select, 
            vad_select, punc_select, spk_select, hotword_input, 
            mode_select, speaker_enable_checkbox, preset_spk_num_input
        ],
        outputs=[text_output, text_output]
    )

if __name__ == "__main__":
    demo.launch(inbrowser=True, server_port=7861) 