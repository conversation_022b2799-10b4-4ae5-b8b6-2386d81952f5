# LightTTS语音识别WebUI使用说明

## 🎯 功能概述

这是基于Gradio的语音识别WebUI，提供了现代化的Web界面来使用FunASR进行语音识别。支持：

- 🎵 多格式音频文件上传
- 🤖 多种识别模型选择（Paraformer、Seaco Paraformer）
- 👥 说话人识别和分离
- ⏰ 时间戳标注
- 🔥 热词定制（Seaco模型）
- 📝 实时可编辑的结果表格
- 💾 TXT/SRT格式下载

## 🚀 快速启动

```bash
cd paraformer_spk_timestamp
python webui_asr_editable.py
```

启动后，浏览器会自动打开 `http://localhost:7860`

## 📋 依赖要求

确保已安装以下Python包：
```bash
pip install gradio pandas funasr
```

## 🎛️ 界面功能说明

### 左侧控制面板

#### 📁 音频文件上传
- 支持格式：WAV, MP3, FLAC, M4A, PCM
- 拖拽或点击上传

#### ⚙️ 基础配置
- **模型类型**：选择Paraformer或Seaco Paraformer
- **运行设备**：CPU或CUDA（自动检测最优选择）

#### 🔥 热词配置（仅Seaco模型）
- 输入自定义热词，用空格分隔
- 提高特定词汇的识别准确率

#### 👥 说话人识别
- **启用开关**：控制是否进行说话人识别
- **说话人数量**：0表示自动识别，1-20指定具体数量

#### 🤖 模型配置
- **ASR模型**：主要的语音识别模型
- **VAD模型**：语音活动检测模型（可选）
- **标点模型**：标点符号添加模型（可选）
- **说话人模型**：说话人分离模型（可选）

#### 📊 程序状态
- 实时显示识别进度和状态信息
- 支持复制状态内容

### 右侧结果面板

#### 📋 识别结果表格
- **固定显示**：固定显示25行数据，无滚动条
- **限制编辑**：只有说话人和识别文本列可以编辑
- **动态列**：根据说话人识别启用状态显示不同列数
- **手动保存**：编辑后需点击保存修改按钮

#### 💾 下载功能
- **📄 下载TXT**：纯文本格式，包含说话人标签
- **🎬 下载SRT**：字幕格式，包含时间戳和说话人
- **🔄 重置到原始**：恢复表格到识别初始状态

#### ✏️ 编辑功能
- **💾 保存修改**：手动保存表格编辑更改
- **限制编辑**：只允许编辑说话人和识别文本列
- **即时生效**：保存后立即应用到下载结果

## 🔧 使用技巧

### 模型选择建议
1. **Paraformer-zh-spk**：适合中文语音识别，性能稳定
2. **Seaco Paraformer**：支持热词定制，适合专业词汇较多的场景

### 设备选择建议
- **CUDA**：有独显的情况下推荐，速度更快
- **CPU**：兼容性好，Mac系统建议选择

### 说话人识别优化
- 音频质量越好，说话人分离效果越佳
- 预设说话人数量可提高识别准确性
- 可以手动编辑表格中的说话人标签

### 热词使用技巧
- 输入专业术语、人名、地名等
- 用空格分隔多个热词
- 热词不宜过多，建议10-20个

## 📝 编辑功能

### 表格编辑
- **限制编辑**：只有说话人列和识别文本列可以编辑
- **双击编辑**：双击可编辑单元格进入编辑模式
- **固定显示**：表格固定显示25行，无滚动条
- **手动保存**：编辑完成后需点击"💾 保存修改"按钮

### 保存机制
- **手动触发**：编辑不会自动保存，需手动点击保存按钮
- **即时生效**：保存后立即更新下载结果
- **状态反馈**：保存成功后在状态框显示确认信息

### 重置功能
- 点击"重置到原始"可恢复到识别初始状态
- 清除所有手动编辑的内容

## 🚨 常见问题

### 1. 模型加载失败
- 检查 `model.conf` 文件是否存在
- 确认模型路径配置正确
- 检查模型文件是否完整

### 2. CUDA相关错误
- 确认已安装CUDA和对应的PyTorch版本
- 如有问题可切换到CPU模式

### 3. 音频格式不支持
- 转换为支持的格式：WAV, MP3, FLAC等
- 确保音频文件没有损坏

### 4. 识别结果为空
- 检查音频是否包含有效语音
- 尝试调整不同的模型组合
- 确认音频音量适中

## 🔒 网络访问

- 默认绑定到 `0.0.0.0:7860`，支持局域网访问
- 如需修改端口，编辑 `webui_asr_editable.py` 中的 `server_port` 参数
- 生产环境建议配置反向代理和访问控制

## 📞 技术支持

如遇到问题，请检查：
1. Python环境和依赖包版本
2. 模型配置文件格式
3. 音频文件格式和质量
4. 系统硬件配置

祝您使用愉快！🎉 