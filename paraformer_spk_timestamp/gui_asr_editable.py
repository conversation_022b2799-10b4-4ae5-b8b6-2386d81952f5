import sys
import os
import configparser
import platform
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QLineEdit, QRadioButton, QButtonGroup, QCheckBox, QSpinBox,
    QGroupBox, QComboBox, QTextEdit, QFileDialog, QFormLayout, QTableWidget, 
    QTableWidgetItem, QHeaderView, QMenu
)
from PySide6.QtCore import QThread, Signal, Qt
from PySide6.QtGui import QScreen # Added for centering window
import time
import funasr

class AsrGui(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("LightTTS语音识别客户端")

        # 居中窗口
        # 获取主屏幕
        screen = QApplication.primaryScreen()
        if screen:
            # 获取屏幕的可用几何区域（考虑任务栏等）
            available_geometry = screen.availableGeometry()
            
            # 期望的窗口大小
            window_width = 900
            window_height = 600
            
            # 计算中心位置
            # available_geometry的x()和y()是可用空间的左上角坐标
            x_pos = available_geometry.x() + (available_geometry.width() - window_width) / 2
            y_pos = available_geometry.y() + (available_geometry.height() - window_height) / 2
            
            self.setGeometry(int(x_pos), int(y_pos), window_width, window_height)
        else:
            # 如果屏幕信息不可用，则使用备选方案
            self.setGeometry(100, 100, 900, 600) # 原始位置

        # 初始化主窗口组件
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        # 创建主布局（左右分栏）
        self.main_layout = QHBoxLayout(self.central_widget)
        self.main_layout.setContentsMargins(10, 10, 10, 10)  # 设置外边距
        self.main_layout.setSpacing(15)  # 左右面板之间的间距

        # 创建左右面板
        self.left_panel = QWidget()
        self.right_panel = QWidget()
        
        # 设置左右面板的布局
        self.left_layout = QVBoxLayout(self.left_panel)
        self.left_layout.setContentsMargins(5, 10, 5, 10)  # 减少左右边距
        self.right_layout = QVBoxLayout(self.right_panel)
        
        # 将左右面板添加到主布局
        self.main_layout.addWidget(self.left_panel, 50)  # 左侧面板占据50%宽度（增加比例以容纳状态区域）
        self.main_layout.addWidget(self.right_panel, 50)  # 右侧面板占据50%宽度

        # 模型缓存和配置数据存储
        self.asr_models_config = {}
        self.vad_models_config = {}
        self.punc_models_config = {}
        self.spk_models_config = {}
        self.model_cache = {}

        # --- 左侧控制面板组件构建 ---
        # 标题和描述
        self.title_label = QLabel("语音识别 (带时间戳和说话人)")
        self.title_label.setStyleSheet("font-size: 16pt; font-weight: bold;")
        
        self.description_label = QLabel("上传音频文件，选择运行设备和模型进行识别。可选择输出 SRT 格式，并可指定说话人数量。")
        self.description_label.setWordWrap(True)
        
        # 将标题和描述添加到左侧面板
        self.left_layout.addWidget(self.title_label)
        self.left_layout.addWidget(self.description_label)
        self.left_layout.addSpacing(15)

        # 音频文件选择
        self.audio_path_edit = QLineEdit()
        self.audio_path_edit.setPlaceholderText("请选择音频文件路径")
        self.audio_path_edit.setReadOnly(True)
        
        self.browse_button = QPushButton("浏览文件")
        self.browse_button.clicked.connect(self.browse_audio_file)
        
        # 创建文件选择布局
        audio_input_layout = QHBoxLayout()
        audio_input_layout.addWidget(self.audio_path_edit)
        audio_input_layout.addWidget(self.browse_button)
        self.left_layout.addLayout(audio_input_layout)
        self.left_layout.addSpacing(15)

        # 基础配置区域（模型类型和设备选择）
        basic_config_layout = QVBoxLayout()
        
        # 第一行：模型类型选择
        model_type_layout = QHBoxLayout()
        self.model_type_label = QLabel("模型类型:")
        self.model_type_combo = QComboBox()
        self.model_type_combo.addItem("Paraformer-zh-spk", "paraformer")
        self.model_type_combo.addItem("Seaco Paraformer", "seaco")
        self.model_type_combo.setCurrentIndex(0)  # 默认选择第一个
        self.model_type_combo.currentTextChanged.connect(self.on_model_type_changed)
        
        model_type_layout.addWidget(self.model_type_label)
        model_type_layout.addWidget(self.model_type_combo)
        model_type_layout.addStretch()
        
        # 第二行：设备选择
        device_layout = QHBoxLayout()
        self.device_label = QLabel("运行设备:")
        self.device_combo = QComboBox()
        self.device_combo.addItem("CPU", "cpu")
        self.device_combo.addItem("CUDA", "cuda")
        
        # 根据操作系统设置默认设备
        is_mac = platform.system().lower() == 'darwin'
        default_device_index = 0 if is_mac else 1  # Mac默认CPU(0)，其他系统默认CUDA(1)
        self.device_combo.setCurrentIndex(default_device_index)
        
        device_layout.addWidget(self.device_label)
        device_layout.addWidget(self.device_combo)
        device_layout.addStretch()
        
        basic_config_layout.addLayout(model_type_layout)
        basic_config_layout.addLayout(device_layout)
        self.left_layout.addLayout(basic_config_layout)
        self.left_layout.addSpacing(10)

        # 热词输入 (仅 Seaco Paraformer 显示)
        self.hotword_label = QLabel("热词 (空格分隔):")
        self.hotword_edit = QLineEdit()
        self.hotword_edit.setPlaceholderText("请输入热词，使用空格分隔")
        self.left_layout.addWidget(self.hotword_label)
        self.left_layout.addWidget(self.hotword_edit)
        self.left_layout.addSpacing(10)

        # 输出模式和说话人选项
        options_layout = QVBoxLayout()  # 改为垂直布局以容纳更多选项
        
        # 说话人识别选项
        speaker_layout = QHBoxLayout()
        self.speaker_enable_checkbox = QCheckBox("启用说话人识别")
        self.speaker_enable_checkbox.setChecked(True)  # 默认启用
        self.speaker_enable_checkbox.toggled.connect(self.on_speaker_enable_changed)
        
        self.speaker_count_spinbox = QSpinBox()
        self.speaker_count_spinbox.setRange(0, 20)
        self.speaker_count_spinbox.setValue(0)
        self.speaker_count_spinbox.setSpecialValueText("自动识别")
        self.speaker_count_spinbox.setToolTip("设置说话人数量，0表示自动识别")
        
        speaker_layout.addWidget(self.speaker_enable_checkbox)
        speaker_layout.addWidget(QLabel("说话人数:"))
        speaker_layout.addWidget(self.speaker_count_spinbox)
        speaker_layout.addStretch()
        
        options_layout.addLayout(speaker_layout)
        self.left_layout.addLayout(options_layout)
        self.left_layout.addSpacing(10)

        # 模型选择
        self.model_config_group = QGroupBox("模型配置")
        model_config_layout = QFormLayout(self.model_config_group)
        model_config_layout.setVerticalSpacing(15)  # 增加垂直间距
        model_config_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.AllNonFixedFieldsGrow)  # 允许字段增长
        
        # 设置模型选择下拉框
        self.asr_model_combo = QComboBox()
        self.asr_model_combo.setSizeAdjustPolicy(QComboBox.SizeAdjustPolicy.AdjustToContents)  # 根据内容调整大小
        self.asr_model_combo.setMinimumContentsLength(20)  # 设置最小显示长度
        
        self.vad_model_combo = QComboBox()
        self.vad_model_combo.setSizeAdjustPolicy(QComboBox.SizeAdjustPolicy.AdjustToContents)
        self.vad_model_combo.setMinimumContentsLength(20)
        
        self.punc_model_combo = QComboBox()
        self.punc_model_combo.setSizeAdjustPolicy(QComboBox.SizeAdjustPolicy.AdjustToContents)
        self.punc_model_combo.setMinimumContentsLength(20)
        
        self.spk_model_combo = QComboBox()
        self.spk_model_combo.setSizeAdjustPolicy(QComboBox.SizeAdjustPolicy.AdjustToContents)
        self.spk_model_combo.setMinimumContentsLength(20)
        
        model_config_layout.addRow("ASR 模型:", self.asr_model_combo)
        model_config_layout.addRow("VAD 模型:", self.vad_model_combo)
        model_config_layout.addRow("标点模型:", self.punc_model_combo)
        model_config_layout.addRow("说话人模型:", self.spk_model_combo)
        
        self.model_config_group.setMinimumHeight(200)  # 设置最小高度确保显示所有选项
        self.left_layout.addWidget(self.model_config_group)
        self.left_layout.addSpacing(20)

        # 识别按钮
        self.submit_button = QPushButton("开始识别")
        self.submit_button.setFixedHeight(40)
        self.submit_button.setStyleSheet("font-size: 12pt;")
        self.submit_button.clicked.connect(self.start_recognition)
        self.left_layout.addWidget(self.submit_button)
        self.left_layout.addSpacing(15)

        # 程序状态区域（移到左侧面板）
        status_header_layout = QHBoxLayout()
        self.status_label = QLabel("程序状态")
        self.status_label.setStyleSheet("font-size: 14pt; font-weight: bold;")
        
        self.clear_status_button = QPushButton("清空状态")
        self.clear_status_button.setMaximumWidth(80)
        self.clear_status_button.clicked.connect(lambda: self.status_text_edit.clear())
        
        status_header_layout.addWidget(self.status_label)
        status_header_layout.addStretch()
        status_header_layout.addWidget(self.clear_status_button)
        
        self.status_text_edit = QTextEdit()
        self.status_text_edit.setReadOnly(True)
        self.status_text_edit.setPlaceholderText("程序状态信息将显示在这里...")
        self.status_text_edit.setMaximumHeight(200)  # 限制状态区域高度
        
        # 将状态区域添加到左侧面板
        self.left_layout.addLayout(status_header_layout)
        self.left_layout.addWidget(self.status_text_edit)
        self.left_layout.addStretch(1)  # 在底部添加可伸展空间

        # --- 右侧显示面板 ---
        # 识别结果区域
        result_header_layout = QHBoxLayout()
        self.result_label = QLabel("识别结果表格")
        self.result_label.setStyleSheet("font-size: 14pt; font-weight: bold;")
        
        # 下载按钮
        self.download_txt_button = QPushButton("下载TXT")
        self.download_txt_button.setMaximumWidth(80)
        self.download_txt_button.setEnabled(False)  # 初始禁用，有结果后启用
        self.download_txt_button.clicked.connect(self.download_txt_result)
        self.download_txt_button.setToolTip("下载为纯文本格式")
        
        self.download_srt_button = QPushButton("下载SRT")
        self.download_srt_button.setMaximumWidth(80)
        self.download_srt_button.setEnabled(False)  # 初始禁用，有结果后启用
        self.download_srt_button.clicked.connect(self.download_srt_result)
        self.download_srt_button.setToolTip("下载为SRT字幕格式")
        
        result_header_layout.addWidget(self.result_label)
        result_header_layout.addStretch()
        result_header_layout.addWidget(self.download_txt_button)
        result_header_layout.addWidget(self.download_srt_button)
        
        # 添加时间戳表格
        self.result_table = QTableWidget()
        self.result_table.setEditTriggers(QTableWidget.EditTrigger.DoubleClicked | QTableWidget.EditTrigger.EditKeyPressed)
        self.result_table.itemChanged.connect(self._on_table_item_changed)
        self.result_table.itemSelectionChanged.connect(self._on_table_selection_changed)
        self.result_table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.result_table.customContextMenuRequested.connect(self._show_table_context_menu)
        
        # 用于记录编辑前的值
        self.edit_old_values = {}
        
        # 表格操作按钮
        table_actions_layout = QHBoxLayout()
        
        # 撤销按钮
        self.undo_button = QPushButton("撤销")
        self.undo_button.setEnabled(False)
        self.undo_button.clicked.connect(self._undo_edit)
        self.undo_button.setToolTip("撤销上一次编辑操作")
        
        # 恢复按钮
        self.redo_button = QPushButton("恢复")
        self.redo_button.setEnabled(False)
        self.redo_button.clicked.connect(self._redo_edit)
        self.redo_button.setToolTip("恢复撤销的编辑操作")
        
        # 重置编辑按钮
        self.reset_edits_button = QPushButton("重置到原始")
        self.reset_edits_button.setEnabled(False)
        self.reset_edits_button.clicked.connect(self._reset_table_edits)
        self.reset_edits_button.setToolTip("清除所有编辑，恢复到原始识别结果")
        
        table_actions_layout.addStretch()
        table_actions_layout.addWidget(self.undo_button)
        table_actions_layout.addWidget(self.redo_button)
        table_actions_layout.addWidget(self.reset_edits_button)
        
        # 添加到右侧面板
        self.right_layout.addLayout(result_header_layout)
        self.right_layout.addWidget(self.result_table)
        self.right_layout.addLayout(table_actions_layout)
        
        # 设置左侧面板的尺寸限制
        from PySide6.QtWidgets import QSizePolicy
        self.left_panel.setMinimumWidth(400)  # 增加控制面板最小宽度以容纳状态区域
        self.left_panel.setMaximumWidth(600)  # 增加控制面板最大宽度
        
        # 右侧面板应可伸展
        self.right_panel.setSizePolicy(
            QSizePolicy.Policy.Expanding,
            QSizePolicy.Policy.Expanding
        )
        
        # 存储当前识别结果的信息，用于下载功能
        self.current_recognition_result = ""
        self.current_audio_filename = ""
        self.current_output_mode = "timestamp"
        
        # 编辑历史管理，用于撤销/恢复功能
        self.edit_history = []  # 存储编辑历史的栈
        self.redo_history = []  # 存储撤销操作的栈
        self.max_history_size = 50  # 最大历史记录数量

        # 初始化界面状态（包含模型配置加载）
        self.on_model_type_changed()

    def browse_audio_file(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "选择音频文件", "", "音频文件 (*.wav *.mp3 *.flac *.m4a *.pcm)")
        if file_path:
            self.audio_path_edit.setText(file_path)
            self.status_text_edit.append(f"已选择音频文件: {file_path}")

    def get_current_model_type(self):
        """获取当前选择的模型类型"""
        return self.model_type_combo.currentData()

    def on_speaker_enable_changed(self):
        """说话人识别启用状态改变时更新界面"""
        # 仅根据启用状态更新"说话人数"控件的可见性
        enabled = self.speaker_enable_checkbox.isChecked()
        self.speaker_count_spinbox.setVisible(enabled)

    def on_model_type_changed(self):
        """模型类型改变时更新界面"""
        model_type = self.get_current_model_type()
        
        # 根据模型类型设置界面显示
        ui_config = {
            "paraformer": {
                "title": "Paraformer 语音识别 \n(带时间戳和说话人)",
                "description": "上传音频文件，选择运行设备和模型进行识别。可选择输出格式，并可启用说话人识别。",
                "show_hotword": False,
                "show_speaker": True,
                "show_punc_spk": True
            },
            "seaco": {
                "title": "Seaco Paraformer 语音识别 \n(带时间戳、说话人和热词)",
                "description": "上传音频文件，选择运行设备和模型进行识别。支持自定义热词、多种输出格式和说话人识别。",
                "show_hotword": True,
                "show_speaker": True,
                "show_punc_spk": True
            }
        }
        
        config = ui_config[model_type]
        
        # 更新标题和描述
        self.title_label.setText(config["title"])
        self.description_label.setText(config["description"])
        
        # 显示/隐藏组件
        self.hotword_label.setVisible(config["show_hotword"])
        self.hotword_edit.setVisible(config["show_hotword"])
        
        # 说话人相关组件
        self.speaker_enable_checkbox.setVisible(config["show_speaker"])
        speaker_enabled = self.speaker_enable_checkbox.isChecked() and config["show_speaker"]
        self.speaker_count_spinbox.setVisible(speaker_enabled)
        
        # 模型选择组件
        self.punc_model_combo.setVisible(config["show_punc_spk"])
        self.spk_model_combo.setVisible(config["show_punc_spk"])
        
        # 重新加载模型配置
        self.load_model_config()

    def load_model_config(self):
        # 统一的配置文件路径
        config_paths = [
            os.path.join(os.path.dirname(os.path.abspath(__file__)), "model.conf"),
            # os.path.join(os.path.dirname(os.path.abspath(__file__)), "model_mac.conf")
        ]
        
        config = configparser.ConfigParser()
        loaded_path = None
        for path in config_paths:
            if os.path.exists(path):
                try:
                    config.read(path, encoding='utf-8')
                    loaded_path = path
                    break
                except Exception as e:
                    self.status_text_edit.append(f"读取配置文件 {path} 失败: {e}")
                    return

        if not loaded_path:
            self.status_text_edit.append("错误: 未找到 model.conf 文件。请确保它位于项目目录下。")
            self.asr_model_combo.addItem("错误: 未找到 model.conf")
            self.vad_model_combo.addItem("错误: 未找到 model.conf")
            self.punc_model_combo.addItem("错误: 未找到 model.conf")
            self.spk_model_combo.addItem("错误: 未找到 model.conf")
            return

        self.status_text_edit.append(f"成功从 {loaded_path} 加载模型配置。")

        def populate_combo(combo, section_name, model_dict):
            combo.clear()
            model_dict.clear()
            if section_name in config:
                for name, path_or_id in config[section_name].items():
                    combo.addItem(name)
                    model_dict[name] = path_or_id
                if not model_dict:
                     combo.addItem(f"无模型 (在 {section_name})")
            else:
                self.status_text_edit.append(f"警告: 配置文件中未找到 [{section_name}] 部分。")
                combo.addItem(f"无模型 (无 {section_name})")

        model_type = self.get_current_model_type()
        
        # 根据模型类型选择对应的ASR模型section
        if model_type == "paraformer":
            populate_combo(self.asr_model_combo, "asr_models_dir", self.asr_models_config)
        elif model_type == "seaco":
            populate_combo(self.asr_model_combo, "asr_seaco_models_dir", self.asr_models_config)
        
        # VAD模型对所有类型都需要
        populate_combo(self.vad_model_combo, "vad_models_dir", self.vad_models_config)
        
        # 根据模型类型决定是否加载其他模型
        if model_type in ["paraformer", "seaco"]:
            populate_combo(self.punc_model_combo, "punc_models_dir", self.punc_models_config)
            populate_combo(self.spk_model_combo, "spk_models_dir", self.spk_models_config)
            
            # 为可选模型添加"不使用"选项，但默认选择第一个实际模型
            for combo, model_cfg in [(self.vad_model_combo, self.vad_models_config), 
                                     (self.punc_model_combo, self.punc_models_config), 
                                     (self.spk_model_combo, self.spk_models_config)]:
                if combo.count() > 0 and "无模型" not in combo.itemText(0) and "错误:" not in combo.itemText(0):
                    combo.insertItem(0, "None (不使用)")
                    model_cfg["None (不使用)"] = None 
                    # 默认选择第1个实际模型，而不是"None (不使用)"
                    if combo.count() > 1:
                        combo.setCurrentIndex(1)

    def start_recognition(self):
        audio_file = self.audio_path_edit.text()
        if not audio_file or not os.path.exists(audio_file):
            self.status_text_edit.append("错误: 请先选择一个有效的音频文件。")
            return

        device = self.device_combo.currentData()
        
        asr_model_name = self.asr_model_combo.currentText()
        vad_model_name = self.vad_model_combo.currentText()
        punc_model_name = self.punc_model_combo.currentText()
        spk_model_name = self.spk_model_combo.currentText()

        asr_model_path = self.asr_models_config.get(asr_model_name)
        vad_model_path = self.vad_models_config.get(vad_model_name)
        punc_model_path = self.punc_models_config.get(punc_model_name)
        spk_model_path = self.spk_models_config.get(spk_model_name)

        if not asr_model_path or "错误:" in asr_model_name or "无模型" in asr_model_name :
            self.status_text_edit.append("错误: 请选择一个有效的 ASR 模型。")
            return
        
        if "错误:" in vad_model_name or "无模型" in vad_model_name or vad_model_name == "None (不使用)": vad_model_path = None
        if "错误:" in punc_model_name or "无模型" in punc_model_name or punc_model_name == "None (不使用)": punc_model_path = None
        if "错误:" in spk_model_name or "无模型" in spk_model_name or spk_model_name == "None (不使用)": spk_model_path = None

        # 固定使用时间戳模式
        output_mode = "timestamp"
        
        # 说话人识别相关参数
        # 实际识别过程始终启用说话人识别（如果有模型），复选框仅控制结果呈现格式
        speaker_enabled_for_recognition = self.speaker_enable_checkbox.isVisible() and spk_model_path  # 实际识别
        speaker_enabled_for_display = self.speaker_enable_checkbox.isChecked()  # 结果显示
        num_speakers = self.speaker_count_spinbox.value() if speaker_enabled_for_recognition else 0
            
        hotword = self.hotword_edit.text().strip() if self.hotword_edit.isVisible() else ""
        model_type = self.get_current_model_type()

        # 清空结果区域，准备显示新的识别结果
        self.result_table.clearContents()
        self.result_label.setText("识别结果表格")
        self.download_txt_button.setEnabled(False)  # 禁用下载按钮
        self.download_srt_button.setEnabled(False)  # 禁用下载按钮
        
        # 保存当前识别参数用于下载
        self.current_audio_filename = os.path.splitext(os.path.basename(audio_file))[0]
        self.current_output_mode = output_mode
        
        # 状态信息输出到状态区域
        self.status_text_edit.append(f"开始识别: {os.path.basename(audio_file)}")
        self.status_text_edit.append(f"  模型类型: {self.model_type_combo.currentText()}")
        self.status_text_edit.append(f"  设备: {self.device_combo.currentText()}")
        self.status_text_edit.append(f"  ASR 模型: {asr_model_name}")
        self.status_text_edit.append(f"  VAD 模型: {vad_model_name if vad_model_path else '不使用'}")
        
        if model_type in ["paraformer", "seaco"]:
            self.status_text_edit.append(f"  标点模型: {punc_model_name if punc_model_path else '不使用'}")
            if speaker_enabled_for_recognition:
                self.status_text_edit.append(f"  说话人模型: {spk_model_name if spk_model_path else '不使用'}")
                self.status_text_edit.append(f"  说话人数: {num_speakers if num_speakers > 0 else '自动'}")
                if not speaker_enabled_for_display:
                    self.status_text_edit.append(f"  说话人识别: 仅用于提高准确性（不显示）")
            else:
                self.status_text_edit.append(f"  说话人识别: 无模型可用")
            
        if model_type == "seaco" and hotword:
            self.status_text_edit.append(f"  热词: {hotword}")
            
        self.status_text_edit.append(f"  输出模式: 时间戳表格格式")
        self.status_text_edit.append("")

        self.submit_button.setEnabled(False)
        self.submit_button.setText("正在识别中...")

        self.recognition_thread = RecognitionWorker(
            audio_file, device, 
            asr_model_path, vad_model_path, punc_model_path, spk_model_path,
            output_mode, num_speakers, hotword, model_type, self.model_cache, speaker_enabled_for_recognition, speaker_enabled_for_display
        )
        self.recognition_thread.progress.connect(self.update_progress)
        self.recognition_thread.finished.connect(self.recognition_finished)
        self.recognition_thread.error.connect(self.recognition_error)
        self.recognition_thread.start()

    def update_progress(self, message):
        self.status_text_edit.append(message)

    def recognition_finished(self, result_text, processing_time, table_data):
        # 状态信息更新
        self.status_text_edit.append(f"识别完成，耗时: {processing_time:.2f} 秒")
        
        # 更新标题
        self.result_label.setText(f"识别结果表格 (耗时: {processing_time:.2f} 秒)")
        
        # 显示表格数据
        if table_data:
            self._populate_results_table(table_data)
            self.reset_edits_button.setEnabled(True)
            # 保存原始数据用于重置
            self.original_table_data = table_data.copy()
            # 初始化编辑历史
            self._init_edit_history()
            
            # 启用下载按钮
            self.download_txt_button.setEnabled(True)
            self.download_srt_button.setEnabled(True)
        else:
            # 如果没有表格数据，显示空表格
            self.result_table.setRowCount(0)
            self.status_text_edit.append("警告: 没有识别到有效的时间戳数据")
            self.reset_edits_button.setEnabled(False)
            
            # 禁用下载按钮
            self.download_txt_button.setEnabled(False)
            self.download_srt_button.setEnabled(False)
        
        # 保存识别结果
        self.current_recognition_result = result_text
        
        self.submit_button.setEnabled(True)
        self.submit_button.setText("开始识别")

    def recognition_error(self, error_message):
        self.status_text_edit.append(f"--- 错误 ---")
        self.status_text_edit.append(error_message)
        self.submit_button.setEnabled(True)
        self.submit_button.setText("开始识别")

    def download_txt_result(self):
        """下载TXT格式的识别结果"""
        if not self.current_recognition_result:
            self.status_text_edit.append("错误: 没有可下载的识别结果。")
            return

        # 从表格生成纯文本格式
        if self.result_table.rowCount() > 0:
            lines = []
            for row in range(self.result_table.rowCount()):
                # 根据说话人识别启用状态获取不同列的数据
                speaker_col = self._get_speaker_column_index()
                text_col = self._get_text_column_index()
                
                if speaker_col != -1:  # 启用说话人识别
                    speaker_item = self.result_table.item(row, speaker_col)
                    text_item = self.result_table.item(row, text_col)
                    
                    if speaker_item and text_item:
                        speaker = speaker_item.text().strip()
                        text = text_item.text().strip()
                        
                        if speaker:
                            lines.append(f"[{speaker}] {text}")
                        else:
                            lines.append(text)
                else:  # 不启用说话人识别
                    text_item = self.result_table.item(row, text_col)
                    
                    if text_item:
                        text = text_item.text().strip()
                        lines.append(text)
            
            content = '\n'.join(lines) if lines else self.current_recognition_result
        else:
            content = self.current_recognition_result

        # 设置默认文件名
        default_filename = f"{self.current_audio_filename}.txt" if self.current_audio_filename else "recognition_result.txt"
        
        # 打开保存对话框
        file_path, _ = QFileDialog.getSaveFileName(
            self, 
            "保存TXT格式结果", 
            default_filename, 
            "文本文件 (*.txt);;所有文件 (*)"
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.status_text_edit.append(f"TXT格式结果已保存到: {file_path}")
            except Exception as e:
                self.status_text_edit.append(f"保存TXT文件失败: {str(e)}")

    def download_srt_result(self):
        """下载SRT格式的识别结果"""
        if not self.current_recognition_result:
            self.status_text_edit.append("错误: 没有可下载的识别结果。")
            return

        # 从表格生成SRT格式
        if self.result_table.rowCount() > 0:
            lines = []
            speaker_col = self._get_speaker_column_index()
            text_col = self._get_text_column_index()
            
            for row in range(self.result_table.rowCount()):
                id_item = self.result_table.item(row, 0)
                start_item = self.result_table.item(row, 1)
                end_item = self.result_table.item(row, 2)
                text_item = self.result_table.item(row, text_col)
                
                required_items = [id_item, start_item, end_item, text_item]
                speaker_item = None
                
                if speaker_col != -1:  # 启用说话人识别
                    speaker_item = self.result_table.item(row, speaker_col)
                    required_items.append(speaker_item)
                
                if all(item is not None for item in required_items):
                    subtitle_id = id_item.text()
                    start_time = start_item.text()
                    end_time = end_item.text()
                    text = text_item.text().strip()
                    
                    lines.append(subtitle_id)
                    lines.append(f"{start_time} --> {end_time}")
                    
                    if speaker_col != -1 and speaker_item:  # 启用说话人识别且有说话人信息
                        speaker = speaker_item.text().strip()
                        if speaker:
                            lines.append(f"[{speaker}] {text}")
                        else:
                            lines.append(text)
                    else:
                        lines.append(text)
                    lines.append("")
            
            content = '\n'.join(lines) if lines else self.current_recognition_result
        else:
            content = self.current_recognition_result

        # 设置默认文件名
        default_filename = f"{self.current_audio_filename}.srt" if self.current_audio_filename else "recognition_result.srt"
        
        # 打开保存对话框
        file_path, _ = QFileDialog.getSaveFileName(
            self, 
            "保存SRT格式结果", 
            default_filename, 
            "SRT字幕文件 (*.srt);;所有文件 (*)"
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.status_text_edit.append(f"SRT格式结果已保存到: {file_path}")
            except Exception as e:
                self.status_text_edit.append(f"保存SRT文件失败: {str(e)}")

    def download_recognition_result(self):
        """下载识别结果到文件（保留兼容性）"""
        # 为了保持兼容性，默认调用SRT下载
        self.download_srt_result()

    def _get_table_headers(self):
        """根据说话人识别启用状态获取表格列标题"""
        if self.speaker_enable_checkbox.isChecked():
            return ["编号", "开始时间", "结束时间", "说话人", "识别文本"]
        else:
            return ["编号", "开始时间", "结束时间", "识别文本"]
    
    def _get_speaker_column_index(self):
        """获取说话人列索引，如果不启用说话人识别则返回-1"""
        if self.speaker_enable_checkbox.isChecked():
            return 3
        else:
            return -1
    
    def _get_text_column_index(self):
        """获取识别文本列索引"""
        if self.speaker_enable_checkbox.isChecked():
            return 4
        else:
            return 3
    
    def _is_column_editable(self, col_idx):
        """判断某列是否可编辑"""
        speaker_col = self._get_speaker_column_index()
        text_col = self._get_text_column_index()
        return col_idx == speaker_col or col_idx == text_col

    def _populate_results_table(self, table_data):
        """填充识别结果表格"""
        self.result_table.clearContents()
        if not table_data:
            self.result_table.setRowCount(0)
            return

        # 隐藏垂直表头（行号），避免与ID列重复
        self.result_table.verticalHeader().setVisible(False)

        # 设置表格列标题
        headers = self._get_table_headers()
        self.result_table.setColumnCount(len(headers))
        self.result_table.setHorizontalHeaderLabels(headers)
        
        # 填充数据
        self.result_table.setRowCount(len(table_data))
        for row_idx, row_data in enumerate(table_data):
            for col_idx, cell_data in enumerate(row_data):
                item = QTableWidgetItem(str(cell_data))
                # 根据动态列索引判断是否可编辑
                if self._is_column_editable(col_idx):
                    item.setFlags(item.flags() | Qt.ItemFlag.ItemIsEditable)
                    # 为可编辑列添加视觉提示
                    speaker_col = self._get_speaker_column_index()
                    if col_idx == speaker_col:
                        item.setToolTip("双击编辑说话人")
                    else:
                        item.setToolTip("双击编辑内容")
                else:  # 编号、开始时间、结束时间列设为只读
                    item.setFlags(item.flags() & ~Qt.ItemFlag.ItemIsEditable)
                    item.setToolTip("此列不可编辑")
                self.result_table.setItem(row_idx, col_idx, item)
        
        # 设置列宽自适应
        self.result_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)
        # 让识别文本列可以拉伸
        text_col = self._get_text_column_index()
        self.result_table.horizontalHeader().setSectionResizeMode(text_col, QHeaderView.ResizeMode.Stretch)
    
    def _on_table_item_changed(self, item):
        """处理表格项编辑事件"""
        if not item:
            return
        
        row = item.row()
        col = item.column()
        new_text = item.text().strip()
        
        # 记录编辑操作到历史
        self._record_edit(row, col, new_text)
        
        # 验证编辑内容
        speaker_col = self._get_speaker_column_index()
        text_col = self._get_text_column_index()
        
        if col == speaker_col and speaker_col != -1:  # 说话人列（仅在启用时）
            if not new_text:
                item.setText("未知说话人")
            else:
                # 可以添加说话人格式验证，比如限制长度等
                if len(new_text) > 50:
                    item.setText(new_text[:50])
        elif col == text_col:  # 识别文本列
            if not new_text:
                item.setText("")
            # 可以添加文本格式验证
        
        # 可以在这里添加保存到文件或其他持久化操作
        self._update_result_text_from_table()
    
    def _on_table_selection_changed(self):
        """表格选择改变时，记录当前选中项的值（用于编辑历史）"""
        current_item = self.result_table.currentItem()
        if current_item and self._is_column_editable(current_item.column()):
            key = (current_item.row(), current_item.column())
            self.edit_old_values[key] = current_item.text()
    
    def _record_edit(self, row, col, new_text):
        """记录编辑操作到历史栈"""
        # 获取编辑前的旧值
        key = (row, col)
        old_text = self.edit_old_values.get(key, "")
        
        # 如果新值和旧值相同，不记录编辑
        if old_text == new_text:
            return
            
        # 记录编辑操作
        edit_record = {
            'row': row,
            'col': col,
            'old_text': old_text,
            'new_text': new_text
        }
        
        # 添加到历史栈
        self.edit_history.append(edit_record)
        
        # 限制历史栈大小
        if len(self.edit_history) > self.max_history_size:
            self.edit_history.pop(0)
        
        # 清空重做栈（新的编辑操作会清空重做历史）
        self.redo_history = []
        
        # 更新按钮状态
        self._update_edit_buttons_state()
        
        # 清理旧值记录
        if key in self.edit_old_values:
            del self.edit_old_values[key]
    
    def _update_edit_buttons_state(self):
        """更新撤销/恢复按钮的状态"""
        self.undo_button.setEnabled(len(self.edit_history) > 0)
        self.redo_button.setEnabled(len(self.redo_history) > 0)
    
    def _undo_edit(self):
        """撤销上一次编辑操作"""
        if not self.edit_history:
            return
        
        # 获取最后一次编辑操作
        edit_record = self.edit_history.pop()
        
        # 临时断开信号连接，避免触发itemChanged
        self.result_table.itemChanged.disconnect(self._on_table_item_changed)
        
        if edit_record.get('type') == 'batch_replace':
            # 处理批量替换的撤销
            speaker_col = self._get_speaker_column_index()
            if speaker_col != -1:
                affected_rows = edit_record['affected_rows']
                old_speaker = edit_record['old_speaker']
                new_speaker = edit_record['new_speaker']
                
                # 将所有受影响的行恢复为旧的说话人
                for row in affected_rows:
                    speaker_item = self.result_table.item(row, speaker_col)
                    if speaker_item and speaker_item.text() == new_speaker:
                        speaker_item.setText(old_speaker)
                
                # 创建重做记录
                redo_record = edit_record.copy()
                self.redo_history.append(redo_record)
                
                self.status_text_edit.append(f"已撤销批量替换：'{new_speaker}' -> '{old_speaker}'")
        else:
            # 处理单个编辑的撤销
            current_item = self.result_table.item(edit_record['row'], edit_record['col'])
            
            # 创建重做记录 - 保持原始的old_text和new_text
            redo_record = {
                'row': edit_record['row'],
                'col': edit_record['col'],
                'old_text': edit_record['old_text'],  # 保持原始的old_text
                'new_text': edit_record['new_text']   # 保持原始的new_text
            }
            self.redo_history.append(redo_record)
            
            # 恢复旧值
            if current_item:
                current_item.setText(edit_record['old_text'])
            
            self.status_text_edit.append(f"已撤销对第{edit_record['row']+1}行的编辑")
        
        # 重新连接信号
        self.result_table.itemChanged.connect(self._on_table_item_changed)
        
        # 更新按钮状态
        self._update_edit_buttons_state()
        
        # 更新下载数据
        self._update_result_text_from_table()
    
    def _redo_edit(self):
        """恢复撤销的编辑操作"""
        if not self.redo_history:
            return
        
        # 获取最后一次撤销的操作
        redo_record = self.redo_history.pop()
        
        # 临时断开信号连接，避免触发itemChanged
        self.result_table.itemChanged.disconnect(self._on_table_item_changed)
        
        if redo_record.get('type') == 'batch_replace':
            # 处理批量替换的恢复
            speaker_col = self._get_speaker_column_index()
            if speaker_col != -1:
                affected_rows = redo_record['affected_rows']
                old_speaker = redo_record['old_speaker']
                new_speaker = redo_record['new_speaker']
                
                # 将所有受影响的行重新设置为新的说话人
                for row in affected_rows:
                    speaker_item = self.result_table.item(row, speaker_col)
                    if speaker_item and speaker_item.text() == old_speaker:
                        speaker_item.setText(new_speaker)
                
                # 重新添加到编辑历史
                self.edit_history.append(redo_record)
                
                self.status_text_edit.append(f"已恢复批量替换：'{old_speaker}' -> '{new_speaker}'")
        else:
            # 处理单个编辑的恢复
            item = self.result_table.item(redo_record['row'], redo_record['col'])
            if item:
                item.setText(redo_record['new_text'])
            
            # 重新添加到编辑历史
            edit_record = {
                'row': redo_record['row'],
                'col': redo_record['col'],
                'old_text': redo_record['old_text'],
                'new_text': redo_record['new_text']
            }
            self.edit_history.append(edit_record)
            
            self.status_text_edit.append(f"已恢复对第{redo_record['row']+1}行的编辑")
        
        # 重新连接信号
        self.result_table.itemChanged.connect(self._on_table_item_changed)
        
        # 更新按钮状态
        self._update_edit_buttons_state()
        
        # 更新下载数据
        self._update_result_text_from_table()
    
    def _update_result_text_from_table(self):
        """根据表格数据更新内部数据结构（用于下载功能）"""
        if not self.result_table.isVisible() or self.result_table.rowCount() == 0:
            return
        
        # 重新生成SRT格式的结果文本用于下载
        lines = []
        for row in range(self.result_table.rowCount()):
            id_item = self.result_table.item(row, 0)
            start_time_item = self.result_table.item(row, 1)
            end_time_item = self.result_table.item(row, 2)
            
            # 根据说话人识别启用状态获取不同列的数据
            speaker_col = self._get_speaker_column_index()
            text_col = self._get_text_column_index()
            
            if speaker_col != -1:  # 启用说话人识别
                speaker_item = self.result_table.item(row, speaker_col)
                text_item = self.result_table.item(row, text_col)
                
                if all(item is not None for item in [id_item, start_time_item, end_time_item, speaker_item, text_item]):
                    subtitle_id = id_item.text()
                    start_time = start_time_item.text()
                    end_time = end_time_item.text()
                    speaker = speaker_item.text().strip()
                    text = text_item.text().strip()
                    
                    # 生成SRT格式
                    lines.append(subtitle_id)
                    lines.append(f"{start_time} --> {end_time}")
                    if speaker:
                        lines.append(f"[{speaker}] {text}")
                    else:
                        lines.append(text)
                    lines.append("")
            else:  # 不启用说话人识别
                text_item = self.result_table.item(row, text_col)
                
                if all(item is not None for item in [id_item, start_time_item, end_time_item, text_item]):
                    subtitle_id = id_item.text()
                    start_time = start_time_item.text()
                    end_time = end_time_item.text()
                    text = text_item.text().strip()
                    
                    # 生成SRT格式
                    lines.append(subtitle_id)
                    lines.append(f"{start_time} --> {end_time}")
                    lines.append(text)
                    lines.append("")
        
        # 更新当前识别结果用于下载
        if lines:
            self.current_recognition_result = '\n'.join(lines)
    
    def _show_table_context_menu(self, position):
        """显示表格右键菜单"""
        if self.result_table.rowCount() == 0:
            return
        
        menu = QMenu(self)
        
        # 获取当前选中的项
        current_item = self.result_table.itemAt(position)
        if current_item:
            row = current_item.row()
            col = current_item.column()
            
            # 根据动态列索引判断是否为可编辑列
            speaker_col = self._get_speaker_column_index()
            text_col = self._get_text_column_index()
            
            # 只在可编辑列显示编辑菜单
            if self._is_column_editable(col):
                edit_action = menu.addAction("编辑此项")
                edit_action.triggered.connect(lambda: self.result_table.editItem(current_item))
                
                # 只有在启用说话人识别且当前列是说话人列时显示批量替换
                if speaker_col != -1 and col == speaker_col:
                    menu.addSeparator()
                    replace_speaker_action = menu.addAction("批量替换说话人")
                    replace_speaker_action.triggered.connect(lambda: self._batch_replace_speaker(row))
        
        # 通用菜单项
        menu.addSeparator()
        copy_row_action = menu.addAction("复制此行")
        copy_row_action.triggered.connect(lambda: self._copy_table_row(current_item.row() if current_item else 0))
        
        menu.exec(self.result_table.mapToGlobal(position))
    
    def _batch_replace_speaker(self, current_row):
        """批量替换说话人"""
        # 确保说话人识别已启用
        speaker_col = self._get_speaker_column_index()
        if speaker_col == -1:
            self.status_text_edit.append("错误: 说话人识别未启用，无法进行批量替换。")
            return
            
        if current_row >= self.result_table.rowCount():
            return
        
        current_speaker_item = self.result_table.item(current_row, speaker_col)
        if not current_speaker_item:
            return
        
        old_speaker = current_speaker_item.text()
        
        # 简单的输入对话框 (在实际应用中可能需要更复杂的对话框)
        from PySide6.QtWidgets import QInputDialog
        new_speaker, ok = QInputDialog.getText(
            self, 
            "批量替换说话人", 
            f"将所有 '{old_speaker}' 替换为:", 
            text=old_speaker
        )
        
        if ok and new_speaker.strip():
            new_speaker = new_speaker.strip()
            
            # 如果新旧说话人相同，不执行替换
            if new_speaker == old_speaker:
                return
            
            # 记录所有要替换的项目
            replace_items = []
            for row in range(self.result_table.rowCount()):
                speaker_item = self.result_table.item(row, speaker_col)
                if speaker_item and speaker_item.text() == old_speaker:
                    replace_items.append((row, speaker_item))
            
            if not replace_items:
                self.status_text_edit.append("没有找到需要替换的说话人标签")
                return
            
            # 将批量替换作为一个整体操作记录到编辑历史
            batch_edit_record = {
                'type': 'batch_replace',
                'old_speaker': old_speaker,
                'new_speaker': new_speaker,
                'affected_rows': [item[0] for item in replace_items]
            }
            
            # 添加到历史栈
            self.edit_history.append(batch_edit_record)
            
            # 限制历史栈大小
            if len(self.edit_history) > self.max_history_size:
                self.edit_history.pop(0)
            
            # 清空重做栈
            self.redo_history = []
            
            # 临时断开信号连接，避免每次替换都触发itemChanged
            self.result_table.itemChanged.disconnect(self._on_table_item_changed)
            
            # 执行替换
            count = 0
            for row, speaker_item in replace_items:
                speaker_item.setText(new_speaker)
                count += 1
            
            # 重新连接信号
            self.result_table.itemChanged.connect(self._on_table_item_changed)
            
            self.status_text_edit.append(f"已替换 {count} 个说话人标签: '{old_speaker}' -> '{new_speaker}'")
            self._update_result_text_from_table()
    
    def _copy_table_row(self, row):
        """复制表格行到剪贴板"""
        if row >= self.result_table.rowCount():
            return
        
        row_data = []
        # 使用实际的列数而不是硬编码
        for col in range(self.result_table.columnCount()):
            item = self.result_table.item(row, col)
            row_data.append(item.text() if item else "")
        
        # 复制到剪贴板
        from PySide6.QtGui import QClipboard
        clipboard = QApplication.clipboard()
        clipboard.setText('\t'.join(row_data))
        self.status_text_edit.append(f"已复制第 {row + 1} 行到剪贴板")
    
    def _reset_table_edits(self):
        """重置表格编辑，恢复到原始识别结果"""
        if hasattr(self, 'original_table_data') and self.original_table_data:
            self._populate_results_table(self.original_table_data)
            # 清空编辑历史
            self._init_edit_history()
            self._update_edit_buttons_state()
            self.status_text_edit.append("已重置表格内容到原始识别结果")
            self._update_result_text_from_table()
        else:
            self.status_text_edit.append("没有原始数据可重置")

    def _init_edit_history(self):
        """初始化编辑历史"""
        self.edit_history = []
        self.redo_history = []
        self.edit_old_values = {}
        self._update_edit_buttons_state()

class RecognitionWorker(QThread):
    progress = Signal(str)
    finished = Signal(str, float, list)  # 添加 list 参数用于表格数据
    error = Signal(str)

    def __init__(self, audio_file, device, 
                 asr_model_path, vad_model_path, punc_model_path, spk_model_path,
                 output_mode, num_speakers, hotword, model_type, model_cache_dict, speaker_enabled, speaker_display_enabled=None):
        super().__init__()
        self.audio_file = audio_file
        self.device = device
        self.asr_model_path = asr_model_path
        self.vad_model_path = vad_model_path
        self.punc_model_path = punc_model_path
        self.spk_model_path = spk_model_path
        self.output_mode = output_mode
        self.num_speakers = num_speakers
        self.hotword = hotword
        self.model_type = model_type
        self.model_cache = model_cache_dict
        self.speaker_enabled = speaker_enabled  # 用于识别过程
        self.speaker_display_enabled = speaker_display_enabled if speaker_display_enabled is not None else speaker_enabled  # 用于显示格式

    def _format_timestamp(self, milliseconds, srt_format=False):
        """Converts milliseconds to HH:MM:SS,ms or HH:MM:SS.ms format."""
        seconds = milliseconds // 1000
        ms = milliseconds % 1000
        minutes = seconds // 60
        seconds %= 60
        hours = minutes // 60
        minutes %= 60
        if srt_format:
            return f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d},{int(ms):03d}"
        else:
            return f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}.{int(ms):03d}"

    def run(self):
        try:
            self.progress.emit("正在准备模型...")
            # 模型缓存键应该区分是否启用说话人识别，因为这会影响模型配置
            model_key_parts = [
                self.device,
                self.asr_model_path,
                str(self.vad_model_path),
                str(self.punc_model_path),
                str(self.spk_model_path) if self.speaker_enabled else "no_spk",
                self.model_type,
                str(self.speaker_enabled)  # 添加说话人启用状态到缓存键
            ]
            model_key = "_".join(filter(None, model_key_parts))

            if model_key in self.model_cache:
                model = self.model_cache[model_key]
                self.progress.emit(f"从缓存加载模型")
            else:
                self.progress.emit(f"首次加载模型 (可能需要一些时间)")
                model_kwargs = {
                    "model": self.asr_model_path,
                    "device": self.device,
                    "disable_update": True
                }
                
                # 根据模型类型设置不同的参数
                if self.model_type == "paraformer":
                    model_kwargs["model_revision"] = "master"
                    # 始终启用时间戳，无论是否启用说话人识别
                    model_kwargs["timestamp"] = True
                    if self.vad_model_path:
                        model_kwargs["vad_model"] = self.vad_model_path
                    if self.punc_model_path:
                        model_kwargs["punc_model"] = self.punc_model_path
                    if self.spk_model_path and self.speaker_enabled:
                        model_kwargs["spk_model"] = self.spk_model_path
                        
                elif self.model_type == "seaco":
                    model_kwargs["model_revision"] = "master"
                    # 始终启用时间戳，无论是否启用说话人识别
                    model_kwargs["timestamp"] = True
                    if self.vad_model_path:
                        model_kwargs["vad_model"] = self.vad_model_path
                        model_kwargs["vad_kwargs"] = {"max_single_segment_time": 60000}
                    if self.punc_model_path:
                        model_kwargs["punc_model"] = self.punc_model_path
                    if self.spk_model_path and self.speaker_enabled:
                        model_kwargs["spk_model"] = self.spk_model_path
                        
                model = funasr.AutoModel(**model_kwargs)
                self.model_cache[model_key] = model
                self.progress.emit("模型加载完成.")

            self.progress.emit(f"开始识别音频文件: {os.path.basename(self.audio_file)}")
            
            # 根据模型类型设置不同的识别参数
            generate_kwargs = {"input": self.audio_file}
            
            if self.model_type in ["paraformer", "seaco"]:
                generate_kwargs["cache"] = {}
                generate_kwargs["return_raw_text"] = True
                if self.spk_model_path and self.speaker_enabled and self.num_speakers > 0:
                    generate_kwargs['preset_spk_num'] = self.num_speakers
                if self.model_type == "seaco" and self.hotword:
                    generate_kwargs['hotword'] = self.hotword

            start_process_time = time.time()
            rec_result = model.generate(**generate_kwargs)
            end_process_time = time.time()
            processing_time = end_process_time - start_process_time
            self.progress.emit("原始识别结果获取完毕, 正在格式化...")
            
            # 处理识别结果
            output_lines, table_data = self._process_paraformer_result(rec_result)
            
            self.finished.emit("\n".join(output_lines), processing_time, table_data)

        except Exception as e:
            self.error.emit(f"识别过程中发生错误: {str(e)}\n请检查模型路径、音频文件格式和依赖项是否正确安装。\n错误详情: {type(e).__name__}: {e}")

    def _process_paraformer_result(self, rec_result):
        """处理 Paraformer 和 Seaco Paraformer 的识别结果"""
        
        output_lines = []
        table_data = []  # 用于表格显示的数据
        
        if rec_result:
            subtitle_index = 1
            has_sentence_info = False
            
            for result in rec_result:
                if 'sentence_info' in result:
                    has_sentence_info = True
                    for sentence in result['sentence_info']:
                        spk_value = sentence.get('spk')
                        # 只有在启用说话人识别时才显示说话人标签
                        if self.speaker_enabled and spk_value is not None and isinstance(spk_value, int):
                            speaker = f"spk{spk_value + 1}"
                            speaker_formatted = f"[{speaker}]   " if self.speaker_display_enabled else ""
                            speaker_for_table = speaker if self.speaker_display_enabled else ""
                        else:
                            speaker_formatted = ""
                            speaker_for_table = ""
                            
                        text = sentence.get('text', '').strip().rstrip(',.。，!！?？')
                        start_time = self._format_timestamp(sentence.get('start', 0), srt_format=(self.output_mode == "timestamp"))
                        end_time = self._format_timestamp(sentence.get('end', 0), srt_format=(self.output_mode == "timestamp"))
                        
                        if self.output_mode == "timestamp":
                            output_lines.append(str(subtitle_index))
                            output_lines.append(f"{start_time} --> {end_time}")
                            output_lines.append(f"{speaker_formatted}{text}")
                            output_lines.append("")
                            
                            # 为表格添加数据 - 根据说话人显示启用状态生成不同结构
                            if self.speaker_display_enabled:
                                # 启用说话人显示：5列数据
                                table_data.append([
                                    subtitle_index,
                                    start_time,
                                    end_time,
                                    speaker_for_table,
                                    text
                                ])
                            else:
                                # 不启用说话人显示：4列数据
                                table_data.append([
                                    subtitle_index,
                                    start_time,
                                    end_time,
                                    text
                                ])
                            
                            subtitle_index += 1
                        else:
                            output_lines.append(f"{speaker_formatted}{text}")
                elif 'text' in result:
                    # 如果没有sentence_info但有text，创建一个简单的条目
                    text = result.get('text', '').strip()
                    if text:
                        if self.output_mode == "timestamp":
                            # 没有时间戳信息时，使用默认时间戳
                            start_time = "00:00:00,000"
                            end_time = "00:00:01,000"
                            
                            output_lines.append(str(subtitle_index))
                            output_lines.append(f"{start_time} --> {end_time}")
                            output_lines.append(text)
                            output_lines.append("")
                            
                            # 根据说话人显示启用状态生成不同结构的默认数据
                            if self.speaker_display_enabled:
                                table_data.append([
                                    subtitle_index,
                                    start_time,
                                    end_time,
                                    "",  # 无说话人信息
                                    text
                                ])
                            else:
                                table_data.append([
                                    subtitle_index,
                                    start_time,
                                    end_time,
                                    text
                                ])
                            
                            subtitle_index += 1
                        else:
                            output_lines.append(text)
            
            # 如果没有找到任何sentence_info，但有识别结果，给出提示
            if not has_sentence_info and table_data:
                self.progress.emit("注意: 模型未提供详细的时间戳信息，使用默认时间戳")
        else:
            output_lines.append("识别结果为空。")
        
        return output_lines, table_data

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = AsrGui()
    window.show()
    sys.exit(app.exec())
