
import os
import sys
import configparser
import platform
import time
import threading
import tempfile
import funasr
import gradio as gr
import pandas as pd
from typing import List, Dict, Tuple, Optional, Any
import json
from utils.web_cache_manager import WebCacheManager

class AsrWebUI:
    def __init__(self):
        # 模型缓存和配置数据存储
        self.asr_models_config = {}
        self.vad_models_config = {}
        self.punc_models_config = {}
        self.spk_models_config = {}
        self.model_cache = {}
        
        # 表格数据管理（已移除original_table_data，统一使用缓存系统）
        
        # 当前识别结果
        self.current_recognition_result = ""
        self.current_audio_filename = ""
        
        # Web缓存管理器
        self.cache_manager = WebCacheManager()
        
        # 加载模型配置
        self.load_model_config()
    
    def load_model_config(self):
        """加载模型配置文件"""
        config_paths = [
            os.path.join(os.path.dirname(os.path.abspath(__file__)), "model.conf"),
        ]
        
        config = configparser.ConfigParser()
        loaded_path = None
        for path in config_paths:
            if os.path.exists(path):
                try:
                    config.read(path, encoding='utf-8')
                    loaded_path = path
                    break
                except Exception as e:
                    print(f"读取配置文件 {path} 失败: {e}")
                    return

        if not loaded_path:
            print("错误: 未找到 model.conf 文件。请确保它位于项目目录下。")
            return

        print(f"成功从 {loaded_path} 加载模型配置。")

        def populate_config(section_name, model_dict):
            model_dict.clear()
            if section_name in config:
                for name, path_or_id in config[section_name].items():
                    model_dict[name] = path_or_id
            else:
                print(f"警告: 配置文件中未找到 [{section_name}] 部分。")

        # 加载各类模型配置
        populate_config("asr_models_dir", self.asr_models_config)
        populate_config("asr_seaco_models_dir", self.asr_models_config)  # Seaco模型会覆盖或添加
        populate_config("vad_models_dir", self.vad_models_config)
        populate_config("punc_models_dir", self.punc_models_config)
        populate_config("spk_models_dir", self.spk_models_config)

    def get_model_choices(self, model_type: str):
        """根据模型类型获取对应的模型选项"""
        if model_type == "paraformer":
            # 重新加载paraformer模型
            config = configparser.ConfigParser()
            config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "model.conf")
            if os.path.exists(config_path):
                config.read(config_path, encoding='utf-8')
                asr_models = {}
                if "asr_models_dir" in config:
                    for name, path_or_id in config["asr_models_dir"].items():
                        asr_models[name] = path_or_id
                return list(asr_models.keys())
        elif model_type == "seaco":
            # 重新加载seaco模型
            config = configparser.ConfigParser()
            config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "model.conf")
            if os.path.exists(config_path):
                config.read(config_path, encoding='utf-8')
                asr_models = {}
                if "asr_seaco_models_dir" in config:
                    for name, path_or_id in config["asr_seaco_models_dir"].items():
                        asr_models[name] = path_or_id
                return list(asr_models.keys())
        
        return list(self.asr_models_config.keys())

    def get_vad_model_choices(self):
        """获取VAD模型选项"""
        choices = ["None (不使用)"] + list(self.vad_models_config.keys())
        return choices

    def get_punc_model_choices(self):
        """获取标点模型选项"""
        choices = ["None (不使用)"] + list(self.punc_models_config.keys())
        return choices

    def get_spk_model_choices(self):
        """获取说话人模型选项"""
        choices = ["None (不使用)"] + list(self.spk_models_config.keys())
        return choices

    def update_model_choices(self, model_type: str):
        """根据模型类型更新ASR模型选项"""
        choices = self.get_model_choices(model_type)
        # 同时更新界面显示状态
        hotword_visible = (model_type == "seaco")
        
        return (
            gr.Dropdown(choices=choices, value=choices[0] if choices else None),  # ASR模型下拉框
            gr.Row(visible=hotword_visible),  # 热词输入行
        )

    def format_timestamp(self, milliseconds: int, srt_format: bool = False) -> str:
        """将毫秒转换为时间戳格式"""
        seconds = milliseconds // 1000
        ms = milliseconds % 1000
        minutes = seconds // 60
        seconds %= 60
        hours = minutes // 60
        minutes %= 60
        if srt_format:
            return f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d},{int(ms):03d}"
        else:
            return f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}.{int(ms):03d}"

    def start_recognition(
        self, 
        audio_file,
        model_type: str,
        device: str,
        asr_model: str,
        vad_model: str,
        punc_model: str,
        spk_model: str,
        speaker_enabled: bool,
        speaker_count: int,
        hotword: str,
        progress=gr.Progress()
    ) -> Tuple[str, pd.DataFrame, str]:
        """开始语音识别"""
        
        if not audio_file:
            return "错误: 请先上传音频文件。", pd.DataFrame(), ""
        
        # 获取模型路径
        progress(0.1, desc="准备模型配置...")
        
        # 重新加载配置以获取最新的模型路径
        config = configparser.ConfigParser()
        config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "model.conf")
        if os.path.exists(config_path):
            config.read(config_path, encoding='utf-8')
        
        # 获取ASR模型路径
        if model_type == "paraformer" and "asr_models_dir" in config:
            asr_model_path = config["asr_models_dir"].get(asr_model)
        elif model_type == "seaco" and "asr_seaco_models_dir" in config:
            asr_model_path = config["asr_seaco_models_dir"].get(asr_model)
        else:
            asr_model_path = self.asr_models_config.get(asr_model)
        
        if not asr_model_path:
            return "错误: 请选择一个有效的ASR模型。", pd.DataFrame(), ""
        
        # 获取其他模型路径
        vad_model_path = None if vad_model == "None (不使用)" else self.vad_models_config.get(vad_model)
        punc_model_path = None if punc_model == "None (不使用)" else self.punc_models_config.get(punc_model)
        spk_model_path = None if spk_model == "None (不使用)" else self.spk_models_config.get(spk_model)
        
        # 说话人识别相关参数
        speaker_enabled_for_recognition = speaker_enabled and spk_model_path
        num_speakers = speaker_count if speaker_enabled_for_recognition else 0
        
        # 保存当前识别参数
        self.current_audio_filename = os.path.splitext(os.path.basename(audio_file))[0]
        
        status_info = []
        status_info.append(f"开始识别: {os.path.basename(audio_file)}")
        status_info.append(f"模型类型: {model_type}")
        status_info.append(f"设备: {device}")
        status_info.append(f"ASR模型: {asr_model}")
        status_info.append(f"VAD模型: {vad_model}")
        status_info.append(f"标点模型: {punc_model}")
        status_info.append(f"说话人模型: {spk_model}")
        if speaker_enabled_for_recognition:
            status_info.append(f"说话人数: {num_speakers if num_speakers > 0 else '自动'}")
        if model_type == "seaco" and hotword.strip():
            status_info.append(f"热词: {hotword}")
        status_info.append("")
        
        try:
            progress(0.2, desc="加载识别模型...")
            
            # 构建模型缓存键
            model_key_parts = [
                device, asr_model_path, str(vad_model_path), str(punc_model_path),
                str(spk_model_path) if speaker_enabled_for_recognition else "no_spk",
                model_type, str(speaker_enabled_for_recognition)
            ]
            model_key = "_".join(filter(None, model_key_parts))
            
            if model_key in self.model_cache:
                model = self.model_cache[model_key]
                status_info.append("从缓存加载模型")
            else:
                status_info.append("首次加载模型 (可能需要一些时间)")
                model_kwargs = {
                    "model": asr_model_path,
                    "device": device,
                    "disable_update": True
                }
                
                # 根据模型类型设置参数
                if model_type == "paraformer":
                    model_kwargs["model_revision"] = "master"
                    model_kwargs["timestamp"] = True
                    if vad_model_path:
                        model_kwargs["vad_model"] = vad_model_path
                    if punc_model_path:
                        model_kwargs["punc_model"] = punc_model_path
                    if spk_model_path and speaker_enabled_for_recognition:
                        model_kwargs["spk_model"] = spk_model_path
                        
                elif model_type == "seaco":
                    model_kwargs["model_revision"] = "master"
                    model_kwargs["timestamp"] = True
                    if vad_model_path:
                        model_kwargs["vad_model"] = vad_model_path
                        model_kwargs["vad_kwargs"] = {"max_single_segment_time": 60000}
                    if punc_model_path:
                        model_kwargs["punc_model"] = punc_model_path
                    if spk_model_path and speaker_enabled_for_recognition:
                        model_kwargs["spk_model"] = spk_model_path
                
                model = funasr.AutoModel(**model_kwargs)
                self.model_cache[model_key] = model
                status_info.append("模型加载完成")
            
            progress(0.5, desc="开始语音识别...")
            
            # 准备识别参数
            generate_kwargs = {"input": audio_file}
            
            if model_type in ["paraformer", "seaco"]:
                generate_kwargs["cache"] = {}
                generate_kwargs["return_raw_text"] = True
                if spk_model_path and speaker_enabled_for_recognition and num_speakers > 0:
                    generate_kwargs['preset_spk_num'] = num_speakers
                if model_type == "seaco" and hotword.strip():
                    generate_kwargs['hotword'] = hotword.strip()
            
            start_time = time.time()
            rec_result = model.generate(**generate_kwargs)
            end_time = time.time()
            processing_time = end_time - start_time
            
            progress(0.8, desc="处理识别结果...")
            
            # 处理识别结果
            output_lines, table_data = self._process_recognition_result(
                rec_result, speaker_enabled_for_recognition, speaker_enabled
            )
            
            # 原始数据已通过缓存系统保存，无需额外存储
            
            # 生成结果文本
            self.current_recognition_result = "\n".join(output_lines)
            
            # 创建缓存会话并保存识别结果
            if table_data:
                try:
                    self.cache_manager.create_session(self.current_audio_filename)
                    self.cache_manager.save_recognition_result(table_data, speaker_enabled)
                    status_info.append("识别结果已保存到缓存")
                except Exception as e:
                    status_info.append(f"保存到缓存失败: {e}")
            
            # 创建DataFrame用于显示
            if table_data:
                if speaker_enabled:
                    columns = ["编号", "开始时间", "结束时间", "说话人", "识别文本"]
                else:
                    columns = ["编号", "开始时间", "结束时间", "识别文本"]
                
                df = pd.DataFrame(table_data, columns=columns)
            else:
                df = pd.DataFrame()
            
            status_info.append(f"识别完成，耗时: {processing_time:.2f} 秒")
            final_status = "\n".join(status_info)
            
            progress(1.0, desc="识别完成")
            
            return final_status, df, f"识别结果 (耗时: {processing_time:.2f} 秒)"
            
        except Exception as e:
            error_msg = f"识别过程中发生错误: {str(e)}"
            status_info.append(error_msg)
            return "\n".join(status_info), pd.DataFrame(), ""

    def _process_recognition_result(self, rec_result, speaker_enabled_for_recognition: bool, speaker_display_enabled: bool):
        """处理识别结果"""
        output_lines = []
        table_data = []
        
        if rec_result:
            subtitle_index = 1
            has_sentence_info = False
            
            for result in rec_result:
                if 'sentence_info' in result:
                    has_sentence_info = True
                    for sentence in result['sentence_info']:
                        spk_value = sentence.get('spk')
                        # 只有在启用说话人识别时才显示说话人标签
                        if speaker_enabled_for_recognition and spk_value is not None and isinstance(spk_value, int):
                            speaker = f"spk{spk_value + 1}"
                            speaker_formatted = f"[{speaker}] " if speaker_display_enabled else ""
                            speaker_for_table = speaker if speaker_display_enabled else ""
                        else:
                            speaker_formatted = ""
                            speaker_for_table = ""
                            
                        text = sentence.get('text', '').strip().rstrip(',.。，!！?？')
                        start_time = self.format_timestamp(sentence.get('start', 0), srt_format=True)
                        end_time = self.format_timestamp(sentence.get('end', 0), srt_format=True)
                        
                        # SRT格式输出
                        output_lines.append(str(subtitle_index))
                        output_lines.append(f"{start_time} --> {end_time}")
                        output_lines.append(f"{speaker_formatted}{text}")
                        output_lines.append("")
                        
                        # 为表格添加数据
                        if speaker_display_enabled:
                            table_data.append([
                                subtitle_index, start_time, end_time, speaker_for_table, text
                            ])
                        else:
                            table_data.append([
                                subtitle_index, start_time, end_time, text
                            ])
                        
                        subtitle_index += 1
                        
                elif 'text' in result:
                    # 如果没有sentence_info但有text，创建一个简单的条目
                    text = result.get('text', '').strip()
                    if text:
                        start_time = "00:00:00,000"
                        end_time = "00:00:01,000"
                        
                        output_lines.append(str(subtitle_index))
                        output_lines.append(f"{start_time} --> {end_time}")
                        output_lines.append(text)
                        output_lines.append("")
                        
                        if speaker_display_enabled:
                            table_data.append([subtitle_index, start_time, end_time, "", text])
                        else:
                            table_data.append([subtitle_index, start_time, end_time, text])
                        
                        subtitle_index += 1
        else:
            output_lines.append("识别结果为空。")
        
        return output_lines, table_data

    def download_txt(self, df: pd.DataFrame):
        """下载TXT格式结果（从缓存生成）"""
        try:
            content = self.cache_manager.generate_txt_content()
            if not content:
                return None
            
            filename = f"{self.current_audio_filename}.txt" if self.current_audio_filename else "recognition_result.txt"
            
            # 创建临时文件
            temp_dir = tempfile.gettempdir()
            temp_path = os.path.join(temp_dir, filename)
            with open(temp_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            return temp_path
        except Exception as e:
            print(f"下载TXT失败: {e}")
            return None

    def download_srt(self, df: pd.DataFrame):
        """下载SRT格式结果（从缓存生成）"""
        try:
            content = self.cache_manager.generate_srt_content()
            if not content:
                return None
            
            filename = f"{self.current_audio_filename}.srt" if self.current_audio_filename else "recognition_result.srt"
            
            # 创建临时文件
            temp_dir = tempfile.gettempdir()
            temp_path = os.path.join(temp_dir, filename)
            with open(temp_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            return temp_path
        except Exception as e:
            print(f"下载SRT失败: {e}")
            return None



    def save_table_changes(self, df: pd.DataFrame):
        """保存表格修改到缓存"""
        if df.empty:
            return df, "没有数据可保存"
        
        try:
            # 更新缓存中的片段
            updated_count = 0
            for _, row in df.iterrows():
                segment_id = int(row.iloc[0])  # 编号列
                
                if len(row) >= 5:  # 包含说话人列
                    speaker = str(row.iloc[3]).strip()
                    text = str(row.iloc[4]).strip()
                else:  # 不包含说话人列
                    speaker = ""
                    text = str(row.iloc[3]).strip()
                
                # 更新缓存中的片段
                if self.cache_manager.update_segment(segment_id, speaker=speaker, text=text):
                    updated_count += 1
            
            # 重新生成识别结果用于下载
            self.current_recognition_result = self.cache_manager.generate_srt_content()
            
            return df, f"已保存 {updated_count} 个片段的修改到缓存"
            
        except Exception as e:
            return df, f"保存修改失败: {e}"
    
    def _update_recognition_result_from_dataframe(self, df: pd.DataFrame):
        """根据DataFrame更新识别结果用于下载"""
        if df.empty:
            return
        
        # 重新生成SRT格式的结果文本
        lines = []
        for _, row in df.iterrows():
            if len(row) >= 4:
                subtitle_id = str(row.iloc[0])
                start_time = str(row.iloc[1])
                end_time = str(row.iloc[2])
                
                lines.append(subtitle_id)
                lines.append(f"{start_time} --> {end_time}")
                
                if len(row) >= 5:  # 包含说话人列
                    speaker = str(row.iloc[3]).strip()
                    text = str(row.iloc[4]).strip()
                    if speaker and speaker != "":
                        lines.append(f"[{speaker}] {text}")
                    else:
                        lines.append(text)
                else:  # 不包含说话人列
                    text = str(row.iloc[3]).strip()
                    lines.append(text)
                
                lines.append("")
        
        # 更新当前识别结果
        if lines:
            self.current_recognition_result = '\n'.join(lines)
    
    def refresh_sessions(self):
        """刷新会话列表"""
        try:
            sessions = self.cache_manager.get_sessions_list()
            choices = []
            
            for session in sessions:
                session_name = session.get("session_name", "未知会话")
                audio_filename = session.get("audio_filename", "")
                created_time = session.get("created_time", "")
                total_segments = session.get("total_segments", 0)
                
                display_name = f"{audio_filename} ({created_time}) - {total_segments}段"
                choices.append((display_name, session["session_dir"]))
            
            # 获取当前会话信息
            current_info = ""
            if self.cache_manager.current_session_dir:
                stats = self.cache_manager.get_session_stats()
                if stats:
                    current_info = f"{stats.get('audio_filename', '')} - {stats.get('total_segments', 0)}段 (已修改: {stats.get('last_modified', '')})"
            
            return gr.Dropdown(choices=choices), current_info
            
        except Exception as e:
            return gr.Dropdown(choices=[]), f"刷新失败: {e}"
    
    def load_session(self, session_path: str, speaker_enabled: bool):
        """加载选中的会话"""
        if not session_path:
            return pd.DataFrame(), "请先选择要加载的会话", ""
        
        try:
            # 加载会话
            if self.cache_manager.load_session(session_path):
                # 获取所有片段数据
                segments = self.cache_manager.get_all_segments()
                
                if not segments:
                    return pd.DataFrame(), "会话中没有数据", ""
                
                # 构建表格数据
                table_data = []
                for segment in segments:
                    if speaker_enabled:
                        row = [
                            segment.get("id", ""),
                            segment.get("start_time", ""),
                            segment.get("end_time", ""),
                            segment.get("speaker", ""),
                            segment.get("text", "")
                        ]
                    else:
                        row = [
                            segment.get("id", ""),
                            segment.get("start_time", ""),
                            segment.get("end_time", ""),
                            segment.get("text", "")
                        ]
                    table_data.append(row)
                
                # 创建DataFrame
                if speaker_enabled:
                    columns = ["编号", "开始时间", "结束时间", "说话人", "识别文本"]
                else:
                    columns = ["编号", "开始时间", "结束时间", "识别文本"]
                
                df = pd.DataFrame(table_data, columns=columns)
                
                # 更新内部状态
                stats = self.cache_manager.get_session_stats()
                self.current_audio_filename = stats.get("audio_filename", "")
                self.current_recognition_result = self.cache_manager.generate_srt_content()
                
                # 生成当前会话信息
                current_info = f"{self.current_audio_filename} - {len(segments)}段"
                
                return df, f"成功加载会话: {stats.get('session_name', '')}", current_info
            else:
                return pd.DataFrame(), "加载会话失败", ""
                
        except Exception as e:
            return pd.DataFrame(), f"加载会话失败: {e}", ""
    
    def delete_session(self, session_path: str):
        """删除选中的会话"""
        if not session_path:
            return "请先选择要删除的会话", gr.Dropdown(choices=[])
        
        try:
            if self.cache_manager.delete_session(session_path):
                # 刷新会话列表
                _, _ = self.refresh_sessions()
                sessions = self.cache_manager.get_sessions_list()
                choices = []
                
                for session in sessions:
                    session_name = session.get("session_name", "未知会话")
                    audio_filename = session.get("audio_filename", "")
                    created_time = session.get("created_time", "")
                    total_segments = session.get("total_segments", 0)
                    
                    display_name = f"{audio_filename} ({created_time}) - {total_segments}段"
                    choices.append((display_name, session["session_dir"]))
                
                return "会话删除成功", gr.Dropdown(choices=choices)
            else:
                return "删除会话失败", gr.Dropdown(choices=[])
                
        except Exception as e:
            return f"删除会话失败: {e}", gr.Dropdown(choices=[])

    def create_interface(self):
        """创建Gradio界面"""
        
        # 自定义CSS样式
        custom_css = """
        .dataframe-container {
            height: 600px !important;
            max-height: 800px !important;
        }
        """

        with gr.Blocks(title="Light语音识别WebUI", theme=gr.themes.Soft(), css=custom_css) as demo:
            
            # 标题
            gr.HTML("""
            <div style="text-align: center; margin-bottom: 20px;">
                <h1 style="color: #2c3e50; margin-bottom: 10px;">🎵 LightASR语音识别WebUI</h1>
                <p style="color: #7f8c8d; font-size: 16px;">
                    上传音频文件，选择运行设备和模型进行识别。支持时间戳、说话人识别和热词功能。
                </p>
            </div>
            """)
            
            with gr.Row():
                # 左侧控制面板
                with gr.Column(scale=3, min_width=400):
                    gr.HTML("<h3>🎛️ 控制面板</h3>")
                    
                    # 音频文件上传
                    audio_file = gr.File(
                        label="📁 上传音频文件",
                        file_types=["audio"],
                        type="filepath"
                    )
                    
                    # 基础配置
                    with gr.Group():
                        gr.HTML("<h4>⚙️ 基础配置</h4>")
                        
                        model_type = gr.Dropdown(
                            choices=[("Paraformer-zh-spk", "paraformer"), ("Seaco Paraformer", "seaco")],
                            value="paraformer",
                            label="模型类型"
                        )
                        
                        device = gr.Dropdown(
                            choices=[("CPU", "cpu"), ("CUDA", "cuda")],
                            value="cuda" if platform.system().lower() != 'darwin' else "cpu",
                            label="运行设备"
                        )
                    
                    # 热词输入（仅Seaco显示）
                    with gr.Row(visible=False) as hotword_row:
                        hotword = gr.Textbox(
                            label="🔥 热词 (空格分隔)",
                            placeholder="请输入热词，使用空格分隔",
                            lines=1
                        )
                    
                    # 说话人识别配置
                    with gr.Group():
                        gr.HTML("<h4>👥 说话人识别</h4>")
                        
                        speaker_enabled = gr.Checkbox(
                            label="启用说话人识别",
                            value=True
                        )
                        
                        speaker_count = gr.Number(
                            label="说话人数量 (0=自动识别)",
                            value=0,
                            minimum=0,
                            maximum=20,
                            step=1
                        )
                    
                    # 模型选择
                    with gr.Group():
                        gr.HTML("<h4>🤖 模型配置</h4>")
                        
                        # 获取默认模型值
                        paraformer_choices = self.get_model_choices("paraformer")
                        vad_choices = self.get_vad_model_choices()
                        punc_choices = self.get_punc_model_choices() 
                        spk_choices = self.get_spk_model_choices()
                        
                        asr_model = gr.Dropdown(
                            choices=paraformer_choices,
                            label="ASR模型",
                            value=paraformer_choices[0] if paraformer_choices else None
                        )
                        
                        vad_model = gr.Dropdown(
                            choices=vad_choices,
                            label="VAD模型", 
                            value=vad_choices[1] if len(vad_choices) > 1 else vad_choices[0] if vad_choices else None
                        )
                        
                        punc_model = gr.Dropdown(
                            choices=punc_choices,
                            label="标点模型",
                            value=punc_choices[1] if len(punc_choices) > 1 else punc_choices[0] if punc_choices else None
                        )
                        
                        spk_model = gr.Dropdown(
                            choices=spk_choices,
                            label="说话人模型",
                            value=spk_choices[1] if len(spk_choices) > 1 else spk_choices[0] if spk_choices else None
                        )
                    
                    # 识别按钮
                    recognize_btn = gr.Button(
                        "🚀 开始识别",
                        variant="primary",
                        size="lg"
                    )
                    
                    # 程序状态
                    gr.HTML("<h4>📊 程序状态</h4>")
                    status_output = gr.Textbox(
                        label="状态信息",
                        lines=8,
                        max_lines=15,
                        show_copy_button=True,
                        interactive=False
                    )
                
                # 右侧结果面板
                with gr.Column(scale=7, min_width=600):
                    gr.HTML("<h3>📋 识别结果</h3>")
                    
                    # 结果标题
                    result_title = gr.HTML("<h4>识别结果表格</h4>")
                    
                    # 表格操作按钮
                    with gr.Row():
                        download_txt_btn = gr.DownloadButton(
                            "📄 下载TXT",
                            variant="secondary",
                            size="sm"
                        )
                        download_srt_btn = gr.DownloadButton(
                            "🎬 下载SRT",
                            variant="secondary",
                            size="sm"
                        )
                    
                    # 编辑操作按钮
                    with gr.Row():
                        save_changes_btn = gr.Button(
                            "💾 保存修改",
                            variant="primary",
                            size="sm"
                        )
                    
                    # 会话管理区域
                    with gr.Group():
                        gr.HTML("<h4>📂 会话管理</h4>")
                        
                        with gr.Row():
                            refresh_sessions_btn = gr.Button("🔄 刷新", size="sm")
                            session_info = gr.Textbox(
                                label="当前会话",
                                placeholder="暂无活动会话",
                                interactive=False,
                                lines=1
                            )
                        
                        sessions_dropdown = gr.Dropdown(
                            label="历史会话",
                            choices=[],
                            interactive=True,
                            allow_custom_value=False
                        )
                        
                        with gr.Row():
                            load_session_btn = gr.Button("📂 加载", variant="secondary", size="sm")
                            delete_session_btn = gr.Button("🗑️ 删除", variant="secondary", size="sm")
                    
                    # 识别结果表格
                    result_table = gr.Dataframe(
                        headers=["编号", "开始时间", "结束时间", "说话人", "识别文本"],
                        datatype=["number", "str", "str", "str", "str"],
                        col_count=(5, "fixed"),
                        row_count=(30, "fixed"),  # 增加显示行数来控制表格高度
                        interactive=True,
                        wrap=True
                    )
                    
                    # 表格编辑提示
                    gr.HTML("""
                    <div style="margin-top: 10px; padding: 10px; background-color: #f8f9fa; border-radius: 5px;">
                        <small style="color: #6c757d;">
                            💡 <strong>使用提示:</strong>
                            双击表格中的<strong>说话人</strong>和<strong>识别文本</strong>列可进行编辑。
                            编辑完成后点击"💾 保存修改"按钮保存更改。如需恢复原始数据，可通过"📂 加载"按钮重新加载会话。
                        </small>
                    </div>
                    """)
            
            # 事件绑定
            model_type.change(
                fn=self.update_model_choices,
                inputs=[model_type],
                outputs=[asr_model, hotword_row]
            )
            
            recognize_btn.click(
                fn=self.start_recognition,
                inputs=[
                    audio_file, model_type, device, asr_model, vad_model, 
                    punc_model, spk_model, speaker_enabled, speaker_count, hotword
                ],
                outputs=[status_output, result_table, result_title]
            )
            
            download_txt_btn.click(
                fn=self.download_txt,
                inputs=[result_table],
                outputs=[download_txt_btn]
            )
            
            download_srt_btn.click(
                fn=self.download_srt,
                inputs=[result_table], 
                outputs=[download_srt_btn]
            )

            save_changes_btn.click(
                fn=self.save_table_changes,
                inputs=[result_table],
                outputs=[result_table, status_output]
            )
            
            # 会话管理事件
            refresh_sessions_btn.click(
                fn=self.refresh_sessions,
                outputs=[sessions_dropdown, session_info]
            )
            
            load_session_btn.click(
                fn=self.load_session,
                inputs=[sessions_dropdown, speaker_enabled],
                outputs=[result_table, status_output, session_info]
            )
            
            delete_session_btn.click(
                fn=self.delete_session,
                inputs=[sessions_dropdown],
                outputs=[status_output, sessions_dropdown]
            )
            
            # 初始化ASR模型选择和会话列表
            demo.load(
                fn=lambda: gr.Dropdown(choices=self.get_model_choices("paraformer"), value=self.get_model_choices("paraformer")[0] if self.get_model_choices("paraformer") else None),
                outputs=[asr_model]
            )
            
            demo.load(
                fn=self.refresh_sessions,
                outputs=[sessions_dropdown, session_info]
            )
        
        return demo

def main():
    """主函数"""
    # 创建WebUI实例
    webui = AsrWebUI()
    
    # 创建界面
    demo = webui.create_interface()
    
    # 启动界面
    demo.launch(
        server_name="0.0.0.0",  # 允许外部访问
        server_port=7860,       # 端口号
        share=False,            # 不创建公共链接
        debug=False,            # 生产环境关闭调试
        favicon_path=None,      # 可以设置自定义图标
        inbrowser=True          # 自动打开浏览器
    )

if __name__ == "__main__":
    main() 
