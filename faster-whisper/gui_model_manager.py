import sys
import os
import shutil
import time
from pathlib import Path
import configparser

from PySide6.QtWidgets import (
    QApplication,
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QTableWidget,
    QTableWidgetItem,
    QPushButton,
    QProgressBar,
    QHeaderView,
    QMessageBox,
    QLabel,
    QSizePolicy,
    QFileDialog # Added QFileDialog import
)
from PySide6.QtCore import Qt, QThread, Signal, QUrl
from PySide6.QtGui import QDesktopServices
from PySide6.QtCore import QDir # Added QDir for default path

# 尝试导入 faster_whisper 相关组件
try:
    from faster_whisper import available_models, download_model, WhisperModel
    FASTER_WHISPER_AVAILABLE = True
    print("Successfully imported faster-whisper components.")
except ImportError:
    print("Failed to import faster-whisper. Using mock functions.")
    FASTER_WHISPER_AVAILABLE = False
    # 如果 faster_whisper 未安装，则使用模拟函数进行GUI开发
    def available_models(): 
        print("Mock: available_models() called")
        return ["tiny", "base", "small", "medium", "large-v2", "large-v3"] # Example models
    
    def download_model(model_name, output_dir=None, cache_dir=None, **kwargs):
        print(f"Mock: download_model for {model_name} to {output_dir or cache_dir or 'default cache'}")
        sim_cache_base = Path(cache_dir or QDir.homePath()) / ".cache" / "sim_hf_hub_cascade"
        sim_cached_model_path = sim_cache_base / f"models--Systran--faster-whisper-{model_name}"
        sim_cached_model_path.mkdir(parents=True, exist_ok=True)
        # 创建模拟 WhisperModel 和验证可能需要的虚拟文件
        (sim_cached_model_path / "model.bin").write_text("模拟模型数据")
        (sim_cached_model_path / "config.json").write_text("{'name': '模拟配置'}")
        (sim_cached_model_path / "tokenizer.json").write_text("{'name': '模拟分词器'}")
        (sim_cached_model_path / "vocabulary.txt").write_text("模拟词汇")
        print(f"模拟：在 {sim_cached_model_path} 中创建了虚拟文件")
        for i in range(3): # 模拟下载时间
            time.sleep(0.5)
            if hasattr(QApplication, 'instance') and QApplication.instance():
                 QApplication.processEvents() 
        print(f"模拟：download_model 为 {model_name} 下载完成。")
        return str(sim_cached_model_path)

    class WhisperModel:
        def __init__(self, model_path_str, device="cpu", **kwargs):
            model_path = Path(model_path_str)
            print(f"模拟：WhisperModel 从 {model_path} 加载到设备 {device}")
            # 基本检查模拟文件
            if not model_path.exists() or \
               not (model_path / "model.bin").exists() or \
               not (model_path / "config.json").exists() or \
               not (model_path / "tokenizer.json").exists():
                 print(f"模拟：在 {model_path} 中找不到必需文件。")
                 raise FileNotFoundError(f"在 {model_path} 中找不到模拟必需文件。")
            time.sleep(0.2) # 模拟加载时间
            print(f"模拟：WhisperModel 从 {model_path} 加载成功")

# 用于快速 GUI 检查模型目录是否可能已填充的核心文件
CORE_MODEL_FILES_FOR_GUI_CHECK = ["model.bin", "config.json", "tokenizer.json"]

class DownloadWorker(QThread):
    status_update = Signal(int, str)    # 行号，状态信息
    progress_update = Signal(int, int)    # 行号，百分比
    download_complete = Signal(int, bool, str, str)    # 行号，是否成功，消息，模型路径

    def __init__(self, row, model_name, target_model_path):
        super().__init__()
        self.row = row
        self.model_name = model_name
        self.target_model_path = Path(target_model_path)
        self._is_cancelled = False

    def run(self):
        print(f"WORKER ({self.model_name}): 启动下载线程为行 {self.row}。")
        try:
            self.status_update.emit(self.row, "正在下载到缓存...")
            self.progress_update.emit(self.row, 0)
            print(f"WORKER ({self.model_name}): 发出初始状态。调用 download_model。")

            cached_model_path_str = download_model(self.model_name)
            print(f"WORKER ({self.model_name}): download_model 返回：{cached_model_path_str}")
            cached_model_path = Path(cached_model_path_str)

            if not cached_model_path.exists() or not any(cached_model_path.iterdir()):
                print(f"WORKER ({self.model_name}): 缓存路径 {cached_model_path_str} 是空的或不存在于 download_model 调用之后。")
                raise Exception(f"缓存路径 {cached_model_path_str} 在 download_model 调用之后无效。")

            if self._is_cancelled:
                print(f"WORKER ({self.model_name}): 下载缓存过程中已取消。")
                self.status_update.emit(self.row, "下载缓存过程中已取消")
                self.download_complete.emit(self.row, False, "下载已取消", str(self.target_model_path))
                return

            self.status_update.emit(self.row, f"已缓存于 {cached_model_path_str}")
            self.progress_update.emit(self.row, 25)
            print(f"WORKER ({self.model_name}): 从缓存 {cached_model_path} 复制到 {self.target_model_path}。")

            if self.target_model_path.exists():
                print(f"WORKER ({self.model_name}): 目标路径 {self.target_model_path} 存在。删除之前复制。")
                shutil.rmtree(self.target_model_path)
            self.target_model_path.mkdir(parents=True, exist_ok=True)

            # 确保我们直接遍历 cached_model_path 中的文件，而不是使用 rglob
            # 因为 download_model 通常返回特定模型的目录
            source_files = [f for f in cached_model_path.iterdir() if f.is_file()]
            if not source_files:
                 print(f"WORKER ({self.model_name}): 没有在缓存路径 {cached_model_path} 中找到文件来复制。")
                 # 如果是类似 models--org--repo/snapshots/hash/ 的常见模式，检查更深一层
                 potential_snapshot_dirs = [d for d in cached_model_path.iterdir() if d.is_dir() and d.name == "snapshots"]
                 if potential_snapshot_dirs and any((potential_snapshot_dirs[0]).iterdir()):
                     actual_cache_content_path = next((potential_snapshot_dirs[0]).iterdir()) # Take the first dir inside snapshots
                     print(f"WORKER ({self.model_name}): 在快照子目录中找到了文件：{actual_cache_content_path}")
                     source_files = [f for f in actual_cache_content_path.iterdir() if f.is_file()]
                 if not source_files: # 仍然没有文件
                      raise Exception(f"在缓存路径 {cached_model_path} 或其直接子目录中没有找到文件来复制。")
            
            total_size = sum(f.stat().st_size for f in source_files)
            copied_size = 0
            print(f"WORKER ({self.model_name}): 总大小为 {total_size} 字节，从 {len(source_files)} 个文件中复制。")

            for item in source_files:
                if self._is_cancelled:
                    print(f"WORKER ({self.model_name}): 复制过程中已取消。")
                    self.cleanup_target_path()
                    self.status_update.emit(self.row, "复制过程中已取消")
                    self.download_complete.emit(self.row, False, "下载已取消", str(self.target_model_path))
                    return
                
                destination_item_path = self.target_model_path / item.name
                # print(f"WORKER ({self.model_name}): 将 {item.name} 复制到 {destination_item_path}") # 可能太冗长
                shutil.copy2(item, destination_item_path)
                copied_size += item.stat().st_size
                if total_size > 0:
                    percentage = int((copied_size / total_size) * 100)
                    self.progress_update.emit(self.row, 25 + int(percentage * 0.5))
            
            print(f"WORKER ({self.model_name}): 复制完成。复制了 {copied_size} 字节。")
            if copied_size == 0 and total_size > 0: # 不应该发生，如果 source_files 不为空
                 raise Exception("复制文件时失败，尽管文件存在。")

            self.progress_update.emit(self.row, 75)
            self.status_update.emit(self.row, "正在验证模型...")
            print(f"WORKER ({self.model_name}): 开始验证。")
            verified, verification_msg = self._verify_model()
            print(f"WORKER ({self.model_name}): 验证结果：{verified}，消息：{verification_msg}")

            if not verified:
                self.cleanup_target_path()
                self.download_complete.emit(self.row, False, f"模型验证失败：{verification_msg}", str(self.target_model_path))
                return
            
            self.progress_update.emit(self.row, 100)
            self.status_update.emit(self.row, "下载完成，正在验证...")
            self.download_complete.emit(self.row, True, "下载和验证成功", str(self.target_model_path))
            print(f"WORKER ({self.model_name}): 下载和验证成功。")

        except Exception as e:
            print(f"WORKER ({self.model_name}): 运行时异常：{str(e)}")
            import traceback
            traceback.print_exc() # 打印完整的跟踪信息以进行调试
            self.cleanup_target_path()
            self.status_update.emit(self.row, f"错误：{str(e)}")
            self.download_complete.emit(self.row, False, f"错误：{str(e)}", str(self.target_model_path))

    def _verify_model(self):
        try:
            # 尝试加载模型。这是最可靠的验证方法。
            # 如果没有 GPU 或未设置 CUDA，使用“cpu”避免问题
            # WhisperModel 类在脚本顶部导入
            # 确保检查 FASTER_WHISPER_AVAILABLE，因为这部分可能在模拟环境中运行
            if FASTER_WHISPER_AVAILABLE:
                _ = WhisperModel(str(self.target_model_path), device="cpu")
            else:
                # 在非 FASTER_WHISPER_AVAILABLE 情况下模拟验证
                # 此模拟应与模拟 WhisperModel 的加载逻辑一致
                if not (self.target_model_path / "model.bin").exists(): # 基本检查模拟
                    raise FileNotFoundError("模拟：model.bin 未找到用于验证")
                print(f"模拟：{self.model_name} 验证成功")
            return True, "模型加载和验证成功"
        except Exception as e:
            return False, f"模型验证失败：{str(e)}"

    def cancel(self):
        self._is_cancelled = True
        self.status_update.emit(self.row, "取消请求...")

    def cleanup_target_path(self):
        if self.target_model_path.exists():
            try:
                shutil.rmtree(self.target_model_path)
                print(f"清理：{self.target_model_path}")
            except Exception as e:
                print(f"清理 {self.target_model_path} 时出错：{e}")

class ModelManagerGUI(QWidget):
    COLUMN_MODEL_NAME = 0
    # COLUMN_SIZE = 1 (已移除)
    COLUMN_STATUS = 1     # 原为 2
    COLUMN_PROGRESS = 2   # 原为 3
    COLUMN_ACTION = 3     # 原为 4
    COLUMN_OPEN_DIR = 4   # 原为 5

    def __init__(self):
        super().__init__()
        self.setWindowTitle("Faster Whisper 模型管理器")
        self.setGeometry(100, 100, 800, 450) # 稍微增加高度以容纳新控件
        self.download_workers = {}
        
        # 读取faster-whisper模型根目录
        config = configparser.ConfigParser()
        conf_path = os.path.join(os.path.dirname(__file__), "model.conf")
        config.read(conf_path, encoding="utf-8")
        faster_whisper_models_root = config["faster_whisper_dir"]["faster_whisper_models_root"]
        
        # 将 models_base_dir 初始化为实例变量，从配置文件读取
        self.models_base_dir = Path(faster_whisper_models_root)
        try:
            self.models_base_dir.mkdir(parents=True, exist_ok=True)
        except OSError as e:
            QMessageBox.critical(self, "初始化错误", f"无法创建默认模型目录 {self.models_base_dir}：{e}")
            # 如果目录创建失败，回退或禁用功能
            self.models_base_dir = Path(QDir.tempPath()) / "faster_whisper_managed_models_temp"
            try:
                self.models_base_dir.mkdir(parents=True, exist_ok=True)
                QMessageBox.warning(self, "目录警告", f"使用临时模型目录：{self.models_base_dir}")
            except OSError as e_temp:
                 QMessageBox.critical(self, "致命错误", f"无法创建临时模型目录：{e_temp}. 退出.")
                 sys.exit(1) # 或者禁用下载/删除功能

        self._init_ui()
        self.populate_models_table()

    def _init_ui(self):
        self.layout = QVBoxLayout(self)

        # 目录选择界面
        dir_selection_layout = QHBoxLayout()
        self.dir_display_label = QLabel(f"下载目录：{self.models_base_dir}")
        self.dir_display_label.setToolTip(str(self.models_base_dir))
        self.dir_display_label.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        self.browse_dir_button = QPushButton("浏览...")
        self.browse_dir_button.clicked.connect(self.select_download_directory)
        dir_selection_layout.addWidget(self.dir_display_label)
        dir_selection_layout.addWidget(self.browse_dir_button)
        self.layout.addLayout(dir_selection_layout)

        self.table = QTableWidget()
        self.table.setColumnCount(5) # 从 6 列调整为 5 列
        self.table.setHorizontalHeaderLabels(["模型名称", "状态", "进度", "操作", "打开目录"]) # 删除“大小”
        self.table.horizontalHeader().setSectionResizeMode(self.COLUMN_MODEL_NAME, QHeaderView.ResizeMode.Stretch)  # 模型名列自动拉伸
        self.table.horizontalHeader().setSectionResizeMode(self.COLUMN_STATUS, QHeaderView.ResizeMode.Stretch) # 状态列会拉伸
        # 进度列可以交互或拉伸
        self.table.horizontalHeader().setSectionResizeMode(self.COLUMN_PROGRESS, QHeaderView.ResizeMode.Interactive)
        self.table.horizontalHeader().setSectionResizeMode(self.COLUMN_ACTION, QHeaderView.ResizeMode.ResizeToContents)
        self.table.horizontalHeader().setSectionResizeMode(self.COLUMN_OPEN_DIR, QHeaderView.ResizeMode.ResizeToContents)

        self.table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)  # 使表格只读
        self.layout.addWidget(self.table)

    def select_download_directory(self):
        new_dir_str = QFileDialog.getExistingDirectory(self, "选择下载目录", str(self.models_base_dir))
        if new_dir_str:
            new_path = Path(new_dir_str)
            if new_path == self.models_base_dir:
                return  # 没有变化
            try:
                new_path.mkdir(parents=True, exist_ok=True)
                # 测试写入权限（可选，但建议）
                test_file = new_path / ".cascade_write_test.tmp"
                test_file.write_text("测试")
                test_file.unlink(missing_ok=True) # 如果 unlink 因为某种原因失败，missing_ok=True 可以确保安全
            except OSError as e:
                QMessageBox.critical(self, "目录错误", f"无法使用目录 '{new_path}'。错误：{e}")
                return

            self.models_base_dir = new_path
            self.dir_display_label.setText(f"下载目录：{self.models_base_dir}")
            self.dir_display_label.setToolTip(str(self.models_base_dir))
            self.populate_models_table() # 使用新目录刷新表格
            QMessageBox.information(self, "目录已更改", f"模型目录已更改为：{self.models_base_dir}")

    def populate_models_table(self):
        try:
            models = available_models()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法获取可用模型：{e}")
            return
        
        self.table.setRowCount(len(models))

        for row, model_name in enumerate(models):
            model_path = self.models_base_dir / model_name # 使用实例变量
            status_text = "未下载"
            action_button_text = "下载"
            is_downloaded = self._is_model_downloaded(model_path)

            if is_downloaded:
                status_text = "已下载"
                action_button_text = "删除"

            self.table.setItem(row, self.COLUMN_MODEL_NAME, QTableWidgetItem(model_name))
            # self.table.setItem(row, self.COLUMN_SIZE, QTableWidgetItem("N/A")) # 删除大小列
            self.table.setItem(row, self.COLUMN_STATUS, QTableWidgetItem(status_text))

            progress_bar = QProgressBar()
            progress_bar.setValue(100 if is_downloaded else 0)
            progress_bar.setTextVisible(False)
            self.table.setCellWidget(row, self.COLUMN_PROGRESS, progress_bar)

            action_button = QPushButton(action_button_text)
            action_button.clicked.connect(lambda checked, r=row, m=model_name, p=model_path: self.handle_action_button(r, m, p))
            self.table.setCellWidget(row, self.COLUMN_ACTION, action_button)
            
            open_dir_button = QPushButton("打开")
            open_dir_button.clicked.connect(lambda checked, p=model_path: self.open_model_directory(p))
            open_dir_button.setEnabled(is_downloaded)
            self.table.setCellWidget(row, self.COLUMN_OPEN_DIR, open_dir_button)

    def _is_model_downloaded(self, model_path):
        if not model_path.exists() or not model_path.is_dir():
            return False
        # 启发式：检查初始 UI 状态下是否存在几个核心文件
        # 严格的检查由 DownloadWorker._verify_model 在下载后完成
        for core_file in CORE_MODEL_FILES_FOR_GUI_CHECK:
            if not (model_path / core_file).exists():
                return False
        return any(model_path.iterdir()) # 确保目录不为空且存在核心文件

    def handle_action_button(self, row, model_name, model_path):
        button = self.table.cellWidget(row, self.COLUMN_ACTION)
        if not button: return

        current_action = button.text()

        if current_action == "下载":
            self.start_download(row, model_name, model_path)
        elif current_action == "取消":
            self.cancel_download(row)
        elif current_action == "删除":
            self.delete_model(row, model_name, model_path)

    def start_download(self, row, model_name, model_path):
        if row in self.download_workers and self.download_workers[row].isRunning():
            QMessageBox.information(self, "信息", f"模型 {model_name} 下载正在进行中。")
            return

        self.table.item(row, self.COLUMN_STATUS).setText("等待...")
        progress_bar = self.table.cellWidget(row, self.COLUMN_PROGRESS)
        if progress_bar: progress_bar.setValue(0)
        
        action_button = self.table.cellWidget(row, self.COLUMN_ACTION)
        if action_button: action_button.setText("取消")

        open_dir_button = self.table.cellWidget(row, self.COLUMN_OPEN_DIR)
        if open_dir_button: open_dir_button.setEnabled(False)

        worker = DownloadWorker(row, model_name, str(model_path))
        worker.status_update.connect(self.update_status_cell)
        worker.progress_update.connect(self.update_progress_cell)
        worker.download_complete.connect(self.on_download_complete)
        self.download_workers[row] = worker
        worker.start()

    def cancel_download(self, row):
        if row in self.download_workers and self.download_workers[row].isRunning():
            self.download_workers[row].cancel()
            # 按钮文本将由 on_download_complete 或 status_update 更新
        else:
            QMessageBox.information(self, "信息", "没有活动下载来取消此模型。")

    def delete_model(self, row, model_name, model_path):
        reply = QMessageBox.question(self, "确认删除", 
                                     f"您确定要删除模型 '{model_name}' 从 {model_path} 吗？",
                                     QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No, QMessageBox.StandardButton.No)
        if reply == QMessageBox.StandardButton.Yes:
            try:
                if model_path.exists():
                    shutil.rmtree(model_path)
                self.update_status_cell(row, "未下载")
                self.update_progress_cell(row, 0)
                action_button = self.table.cellWidget(row, self.COLUMN_ACTION)
                if action_button: action_button.setText("下载")
                open_dir_button = self.table.cellWidget(row, self.COLUMN_OPEN_DIR)
                if open_dir_button: open_dir_button.setEnabled(False)
                # self.update_size_cell(row, "N/A") # 删除大小更新
                QMessageBox.information(self, "已删除", f"模型 '{model_name}' 已成功删除。")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"删除模型 '{model_name}' 失败：{e}")
                self.update_status_cell(row, "删除错误") # 如果删除失败，则还原状态

    def update_status_cell(self, row, message):
        item = self.table.item(row, self.COLUMN_STATUS)
        if item:
            item.setText(message)
        else:
            self.table.setItem(row, self.COLUMN_STATUS, QTableWidgetItem(message))

    def update_progress_cell(self, row, percentage):
        progress_bar = self.table.cellWidget(row, self.COLUMN_PROGRESS)
        if isinstance(progress_bar, QProgressBar):
            progress_bar.setValue(percentage)

    # def update_size_cell 删除

    def on_download_complete(self, row, success, message, model_path_str):
        self.update_status_cell(row, message if not success else "已下载并验证")
        action_button = self.table.cellWidget(row, self.COLUMN_ACTION)
        open_dir_button = self.table.cellWidget(row, self.COLUMN_OPEN_DIR)

        if success:
            self.update_progress_cell(row, 100)
            if action_button: action_button.setText("删除")
            if open_dir_button: open_dir_button.setEnabled(True)
            # self.update_size_cell(row, model_path_str) # 删除大小更新
        else:
            self.update_progress_cell(row, 0) # 失败/取消时重置进度
            if action_button: action_button.setText("下载")
            if open_dir_button: open_dir_button.setEnabled(False)
            # self.update_size_cell(row, "N/A") # 删除大小更新
            if "cancelled" not in message.lower(): # 不显示用户发起的取消操作弹出窗口
                 QMessageBox.warning(self, "下载问题", message)

        if row in self.download_workers:
            del self.download_workers[row]
            
    def open_model_directory(self, model_path):
        if model_path.exists() and model_path.is_dir():
            QDesktopServices.openUrl(QUrl.fromLocalFile(str(model_path)))
        else:
            QMessageBox.warning(self, "未找到", f"目录 {model_path} 不存在。")

    def closeEvent(self, event):
        # 尝试优雅地取消所有正在进行的下载
        active_workers = False
        for row, worker in list(self.download_workers.items()): # 遍历副本
            if worker.isRunning():
                active_workers = True
                worker.cancel()
                # worker.wait(5000) # 等待线程完成，带超时
        
        if active_workers:
            QMessageBox.information(self, "下载进行中", 
                                    "正在尝试取消正在进行的下载。请稍等片刻，等待它们停止。")
            # 给线程一些时间实际取消和清理
            # 这是一个简单的方法；更健壮的解决方案可能涉及更复杂的线程管理
            # 目前，我们只是请求取消并让应用程序关闭
            # 如果线程没有快速停止，它们可能会被突然终止

        super().closeEvent(event)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    # 应用简单样式以获得更好的外观（可选）
    # app.setStyle("Fusion") 
    gui = ModelManagerGUI()
    gui.show()
    sys.exit(app.exec())
