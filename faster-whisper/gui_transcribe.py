import sys
import os
import traceback
import tempfile
import datetime
import time
import configparser  # 新增导入
import platform  # 用于检测操作系统类型

from PySide6.QtWidgets import (
    QApplication,
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QGridLayout,
    QFormLayout,
    QLabel,
    QLineEdit,
    QPushButton,
    QComboBox,
    QTextEdit,
    QTableWidget,
    QTableWidgetItem,
    QFileDialog,
    QMessageBox,
    QHeaderView,
    QGroupBox,
    QRadioButton
)
from PySide6.QtCore import Qt, QThread, Signal

# 设置环境变量以确保正确的编码处理
os.environ['PYTHONIOENCODING'] = 'utf-8'
os.environ['LC_ALL'] = 'C.UTF-8'

try:
    from faster_whisper import WhisperModel
    FASTER_WHISPER_AVAILABLE = True
except ImportError:
    FASTER_WHISPER_AVAILABLE = False
    print("错误: faster-whisper 未安装，请先安装: pip install faster-whisper")
    # 用于UI开发的模拟WhisperModel（如果未安装）
    class WhisperModel:
        def __init__(self, model_size_or_path, device="cpu", compute_type="default", **kwargs):
            print(f"Mock WhisperModel: {model_size_or_path}, Device: {device}, Compute: {compute_type}")
        def transcribe(self, audio, language=None, word_timestamps=False, **kwargs):
            print(f"Mock transcribe: Audio: {audio}, Lang: {language}, WordTS: {word_timestamps}")
            mock_segment = type('Segment', (), {'text': '这是模拟文本', 'start': 0.0, 'end': 1.0, 'words': [type('Word', (), {'word': '模拟', 'start':0.0, 'end':0.5, 'probability':0.9})()] if word_timestamps else None, 'id': 0 })()
            return [mock_segment], type('Info', (), {'language': 'zh', 'language_probability': 0.9, 'duration': 1.0})()

# 定义本地模型的基础目录 (与 webui_transcribe.py 保持一致)
# 从配置文件读取模型根目录
config = configparser.ConfigParser()
conf_path = os.path.join(os.path.dirname(__file__), "model.conf")
config.read(conf_path, encoding="utf-8")
CUSTOM_MODELS_BASE_DIR = config["faster_whisper_dir"]["faster_whisper_models_root"]

# --- 辅助函数 (部分从 webui_transcribe.py 移植) --- #
def get_local_models(base_dir):
    if not base_dir or not os.path.isdir(base_dir):
        # 如果目录为空或不存在，无需显示警告
        return []
    try:
        models = [d for d in os.listdir(base_dir) if os.path.isdir(os.path.join(base_dir, d))]
        # 无论是否找到模型，都不打印警告
        return sorted(models)
    except Exception as e:
        # 发生异常时也不打印错误信息
        return []

def format_srt_timestamp(seconds_float: float) -> str:
    delta = datetime.timedelta(seconds=seconds_float)
    hours, remainder = divmod(delta.seconds, 3600)
    minutes, seconds = divmod(remainder, 60)
    milliseconds = int(delta.microseconds / 1000)
    return f"{hours:02}:{minutes:02}:{seconds:02},{milliseconds:03}"

def generate_txt_content(segments_iterable):
    return "\n".join(segment.text.strip() for segment in segments_iterable if segment.text)

def generate_srt_content(segments_iterable):
    srt_content = []
    for i, segment in enumerate(segments_iterable):
        start_time = format_srt_timestamp(segment.start)
        end_time = format_srt_timestamp(segment.end)
        srt_content.append(str(i + 1))
        srt_content.append(f"{start_time} --> {end_time}")
        srt_content.append(segment.text.strip() if segment.text else "")
        srt_content.append("")
    return "\n".join(srt_content)

def save_temp_file_qt(content: str, extension: str, original_audio_path: str) -> str:
    if not original_audio_path or not isinstance(original_audio_path, str):
        base_name = "transcription_"
    else:
        base_name = os.path.splitext(os.path.basename(original_audio_path))[0]
    
    try:
        with tempfile.NamedTemporaryFile(mode="w", suffix=extension, prefix=base_name + "_", delete=False, encoding='utf-8') as tmp_file:
            tmp_file.write(content)
            return tmp_file.name
    except Exception as e:
        print(f"保存临时文件时出错 ({extension}): {e}")
        return None

def create_safe_audio_path(original_path):
    """创建一个安全的ASCII路径来避免编码问题"""
    try:
        # 检查路径是否包含非ASCII字符
        original_path.encode('ascii')
        # 如果没有抛出异常，说明路径是ASCII安全的
        return original_path, None
    except UnicodeEncodeError:
        # 路径包含非ASCII字符，创建临时符号链接
        try:
            # 获取文件扩展名
            _, ext = os.path.splitext(original_path)
            # 创建一个临时文件名（ASCII安全）
            with tempfile.NamedTemporaryFile(suffix=ext, delete=False) as tmp_file:
                temp_path = tmp_file.name
            
            # 删除临时文件，只保留路径
            os.unlink(temp_path)
            
            # 创建符号链接
            os.symlink(original_path, temp_path)
            return temp_path, temp_path  # 返回临时路径和需要清理的路径
        except Exception as e:
            print(f"创建临时符号链接失败: {e}")
            return original_path, None  # 回退到原始路径

class TranscriptionWorker(QThread):
    progress_update = Signal(str)
    transcription_finished = Signal(tuple) # (full_text, df_data, txt_path, srt_path, duration, output_format)
    error_occurred = Signal(str)

    def __init__(self, audio_path, model_name, device, compute_type, language, output_format, parent=None):
        super().__init__(parent)
        self.audio_path = audio_path
        self.model_name = model_name
        self.device = device
        self.compute_type = compute_type
        self.language = language if language != "自动检测" else None
        self.output_format = output_format  # 'word' or 'segment'
        self.model = None
        self.temp_audio_path = None  # 用于存储临时音频路径

    def run(self):
        try:
            start_time = time.time()
            self.progress_update.emit(f"正在加载模型: {self.model_name}...")
            model_path = os.path.join(CUSTOM_MODELS_BASE_DIR, self.model_name)
            if not os.path.isdir(model_path):
                 # 如果本地未找到模型，则回退使用Hugging Face模型名称（尽管get_local_models应该可以防止这种情况）
                model_path = self.model_name 
            
            self.model = WhisperModel(model_path, device=self.device, compute_type=self.compute_type)
            self.progress_update.emit(f"模型加载完毕。开始转录 ({'词语级' if self.output_format == 'word' else '句级'})...")

            # 使用安全的音频路径处理
            self.progress_update.emit("正在处理音频文件路径...")
            safe_audio_path, temp_path = create_safe_audio_path(self.audio_path)
            self.temp_audio_path = temp_path  # 保存以便后续清理
            
            self.progress_update.emit("正在开始音频转录...")
            segments, info = self.model.transcribe(
                safe_audio_path,
                language=self.language,
                beam_size=5, # 来自faster-whisper示例的默认值
                word_timestamps=(self.output_format == 'word'),
                condition_on_previous_text=False # 默认值
            )
            
            segments_list = list(segments) # 消耗生成器
            duration = time.time() - start_time
            self.progress_update.emit(f"转录完成，耗时: {duration:.2f} 秒. 正在处理结果...")

            full_text = generate_txt_content(segments_list)
            txt_file_path = save_temp_file_qt(full_text, ".txt", self.audio_path)
            srt_content = generate_srt_content(segments_list)
            srt_file_path = save_temp_file_qt(srt_content, ".srt", self.audio_path)

            df_data = []
            if self.output_format == 'word':
                word_id_counter = 0
                for seg_idx, segment in enumerate(segments_list):
                    if segment.words:
                        for word in segment.words:
                            df_data.append([word.word, f"{word.start:.2f}", f"{word.end:.2f}", f"{word.probability:.2f}", seg_idx])
                            word_id_counter += 1
            else: # 段落模式
                for i, segment in enumerate(segments_list):
                    df_data.append([i + 1, f"{segment.start:.2f}", f"{segment.end:.2f}", segment.text.strip()])
            
            self.transcription_finished.emit((full_text, df_data, txt_file_path, srt_file_path, duration, self.output_format))

        except Exception as e:
            print(traceback.format_exc())
            self.error_occurred.emit(f"转录错误: {str(e)}")
        finally:
            # 清理临时音频文件
            if self.temp_audio_path and os.path.exists(self.temp_audio_path):
                try:
                    os.unlink(self.temp_audio_path)
                    print(f"已清理临时音频文件: {self.temp_audio_path}")
                except Exception as e:
                    print(f"清理临时音频文件失败: {e}")
            
            if self.model is not None:
                del self.model # 释放模型资源
            if self.device == "cuda":
                try:
                    import torch
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()
                except ImportError:
                    pass # 未找到PyTorch
                except Exception as e_cuda:
                    print(f"清理CUDA缓存时出错: {e_cuda}")

class TranscriptionAppGUI(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Faster-Whisper 时间戳提取工具 (PyQt6)")
        self.setGeometry(100, 100, 900, 700)
        self.models_dir = CUSTOM_MODELS_BASE_DIR
        self.current_models = []
        self.active_worker = None
        self.temp_files = {}  # 用于存储临时文件路径 {'txt': 'path.txt', 'srt': 'path.srt'}
        self.result_segments = []
        self.result_words = []
        self._init_ui()
        self._load_models()
        if not FASTER_WHISPER_AVAILABLE:
            QMessageBox.critical(self, "依赖缺失", "错误: faster-whisper 库未找到或无法导入。请确保已正确安装。应用程序功能将受限。")

    def _init_ui(self):
        main_layout = QVBoxLayout(self)

        # 1. Global Settings Group
        settings_group = QGroupBox("全局设置")
        settings_layout = QGridLayout()
        
        settings_layout.addWidget(QLabel("选择设备:"), 0, 0)
        self.device_cpu_radio = QRadioButton("CPU")
        self.device_cuda_radio = QRadioButton("CUDA")
        
        # 根据系统类型设置默认设备
        system_type = platform.system()
        if system_type == "Darwin":  # macOS
            self.device_cpu_radio.setChecked(True)
            self.device_cuda_radio.setChecked(False)
        else:  # 其他系统（Linux、Windows等）
            self.device_cpu_radio.setChecked(False)
            self.device_cuda_radio.setChecked(True)
        
        device_h_layout = QHBoxLayout()
        device_h_layout.addWidget(self.device_cpu_radio)
        device_h_layout.addWidget(self.device_cuda_radio)
        settings_layout.addLayout(device_h_layout, 0, 1)

        settings_layout.addWidget(QLabel("计算类型:"), 1, 0)
        self.compute_type_combo = QComboBox()
        self.compute_type_combo.addItems(["default", "int8", "float16", "int8_float16", "int16", "float32"])
        settings_layout.addWidget(self.compute_type_combo, 1, 1)

        settings_layout.addWidget(QLabel("转录语言:"), 2, 0)
        self.language_combo = QComboBox()
        self.language_combo.addItems(["自动检测", "中文 (zh)", "英文 (en)"]) # 根据需要添加更多
        settings_layout.addWidget(self.language_combo, 2, 1)
        
        settings_group.setLayout(settings_layout)
        main_layout.addWidget(settings_group)

        # 模型目录选择
        models_dir_group = QGroupBox("模型目录")
        models_dir_layout = QHBoxLayout(models_dir_group)
        self.models_dir_edit = QLineEdit(self.models_dir)
        self.models_dir_edit.setReadOnly(True)
        models_dir_layout.addWidget(self.models_dir_edit)
        models_dir_button = QPushButton("选择目录...")
        models_dir_button.clicked.connect(self._select_models_directory)
        models_dir_layout.addWidget(models_dir_button)
        main_layout.addWidget(models_dir_group)

        # 2. Input Group
        input_group = QGroupBox("输入")
        input_layout = QFormLayout()

        # 音频文件
        audio_file_layout = QHBoxLayout()
        self.audio_path_edit = QLineEdit()
        self.audio_path_edit.setReadOnly(True)
        audio_file_layout.addWidget(self.audio_path_edit)
        browse_button = QPushButton("浏览...")
        browse_button.clicked.connect(self._browse_audio_file)
        audio_file_layout.addWidget(browse_button)
        input_layout.addRow(QLabel("音频文件:"), audio_file_layout)

        # 模型选择
        self.model_combo = QComboBox()
        input_layout.addRow(QLabel("选择模型:"), self.model_combo)

        # 输出格式选择
        self.output_format_combo = QComboBox()
        self.output_format_combo.addItems(["句级时间戳", "词语时间戳"])
        input_layout.addRow(QLabel("输出格式:"), self.output_format_combo)

        input_group.setLayout(input_layout)
        main_layout.addWidget(input_group)

        # 3. Transcribe button
        self.transcribe_button = QPushButton("开始转录")
        self.transcribe_button.clicked.connect(self._start_transcription)
        main_layout.addWidget(self.transcribe_button)

        # 4. Output group
        output_group = QGroupBox("输出")
        output_layout = QVBoxLayout()
        
        self.output_text_edit = QTextEdit()
        self.output_text_edit.setReadOnly(True)
        self.output_text_edit.setPlaceholderText("转录结果将显示在此处")
        output_layout.addWidget(QLabel("转录文本:"))
        output_layout.addWidget(self.output_text_edit)

        self.output_table = QTableWidget()
        self.output_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        output_layout.addWidget(QLabel("时间戳表格:"))
        output_layout.addWidget(self.output_table)

        download_buttons_layout = QHBoxLayout()
        self.download_txt_button = QPushButton("下载 TXT")
        self.download_txt_button.setEnabled(False)
        self.download_txt_button.clicked.connect(lambda: self._save_output_file('txt'))
        download_buttons_layout.addWidget(self.download_txt_button)

        self.download_srt_button = QPushButton("下载 SRT")
        self.download_srt_button.setEnabled(False)
        self.download_srt_button.clicked.connect(lambda: self._save_output_file('srt'))
        download_buttons_layout.addWidget(self.download_srt_button)
        output_layout.addLayout(download_buttons_layout)
        
        output_group.setLayout(output_layout)
        main_layout.addWidget(output_group)

        # 5. Status Label
        self.status_label = QLabel("状态: 准备就绪")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        main_layout.addWidget(self.status_label)

    def _load_models(self):
        self.current_models = get_local_models(self.models_dir)
        if not self.current_models:  # 如果没有找到本地模型
            self.model_combo.clear()
            self.model_combo.addItems(["请选择模型目录"])
            self.model_combo.setEnabled(False)
            self.status_label.setText(f"状态: 未设置模型目录或目录中无有效模型，请先选择模型目录")
            self.transcribe_button.setEnabled(False)
        else:
            self.model_combo.clear()
            self.model_combo.addItems(self.current_models)
            self.model_combo.setEnabled(True)
            if "tiny" in self.current_models:  # 如果有tiny模型，默认选择它
                self.model_combo.setCurrentText("tiny")
            else:
                self.model_combo.setCurrentIndex(0)  # 否则选择第一个
            self.transcribe_button.setEnabled(True)
            self.status_label.setText(f"状态: 已加载 {len(self.current_models)} 个模型")

    def _select_models_directory(self):
        directory = QFileDialog.getExistingDirectory(self, "选择模型目录", self.models_dir)
        if directory:
            self.models_dir = directory
            self.models_dir_edit.setText(directory)
            self._load_models()  # 重新加载模型列表

    def _browse_audio_file(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "选择音频文件", "", "音频文件 (*.mp3 *.wav *.m4a *.flac);;所有文件 (*)")
        if file_path:
            self.audio_path_edit.setText(file_path)

    def _start_transcription(self):
        if not FASTER_WHISPER_AVAILABLE:
            QMessageBox.critical(self, "错误", "faster-whisper 库不可用，无法执行转录。")
            return

        audio_path = self.audio_path_edit.text()
        model_name = self.model_combo.currentText()
        if not model_name or model_name == "请选择模型目录":  # 无效的模型选择
            self.status_label.setText("状态: 请先选择有效的模型目录并加载模型")
            self.transcribe_button.setEnabled(True)
            return

        # 构建模型完整路径
        model_path = os.path.join(self.models_dir, model_name)
        if not os.path.isdir(model_path):
            self.status_label.setText(f"状态: 错误 - 模型路径无效 '{model_path}'")
            self.transcribe_button.setEnabled(True)
            return

        device = "cuda" if self.device_cuda_radio.isChecked() else "cpu"
        compute_type = self.compute_type_combo.currentText()
        language_full = self.language_combo.currentText()
        language_code = language_full.split(" (")[-1][:-1] if " (" in language_full else language_full
        
        # 获取输出格式
        output_format_text = self.output_format_combo.currentText()
        output_format = "word" if output_format_text == "词语时间戳" else "segment"

        self.transcribe_button.setEnabled(False)
        self.download_txt_button.setEnabled(False)
        self.download_srt_button.setEnabled(False)
        self.status_label.setText("状态: 正在准备转录...")

        self.worker = TranscriptionWorker(audio_path, model_name, device, compute_type, language_code, output_format)
        self.worker.progress_update.connect(self._update_progress)
        self.worker.transcription_finished.connect(self._handle_transcription_finished)
        self.worker.error_occurred.connect(self._handle_error)
        self.worker.start()

    def _update_progress(self, message):
        self.status_label.setText(f"状态: {message}")

    def _handle_transcription_finished(self, result_tuple):
        full_text, df_data, txt_path, srt_path, duration, output_format = result_tuple
        
        self.temp_files['txt'] = txt_path
        self.temp_files['srt'] = srt_path

        self.output_text_edit.setText(full_text)
        self._populate_table(self.output_table, df_data, output_format)

        self.status_label.setText(f"状态: 转录完成! 耗时 {duration:.2f} 秒")
        self.transcribe_button.setEnabled(True)
        if txt_path: self.download_txt_button.setEnabled(True)
        if srt_path: self.download_srt_button.setEnabled(True)

    def _populate_table(self, table_widget, df_data, output_format):
        table_widget.clearContents()
        if not df_data:
            table_widget.setRowCount(0)
            return

        # 隐藏垂直表头（行号），避免与ID列重复
        table_widget.verticalHeader().setVisible(False)

        if output_format == 'word':
            headers = ["词语", "开始 (s)", "结束 (s)", "置信度", "句段ID"]
            table_widget.setColumnCount(len(headers))
            table_widget.setHorizontalHeaderLabels(headers)
            table_widget.setRowCount(len(df_data))
            for row_idx, row_data in enumerate(df_data):
                for col_idx, cell_data in enumerate(row_data):
                    table_widget.setItem(row_idx, col_idx, QTableWidgetItem(str(cell_data)))
        else: # segment
            headers = ["ID", "开始 (s)", "结束 (s)", "文本"]
            table_widget.setColumnCount(len(headers))
            table_widget.setHorizontalHeaderLabels(headers)
            table_widget.setRowCount(len(df_data))
            for row_idx, row_data in enumerate(df_data):
                for col_idx, cell_data in enumerate(row_data):
                    table_widget.setItem(row_idx, col_idx, QTableWidgetItem(str(cell_data)))
        
        table_widget.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)
        if output_format == 'segment' and table_widget.columnCount() > 3:
             table_widget.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch) # 拉伸文本列

    def _handle_error(self, error_message):
        QMessageBox.critical(self, "转录错误", error_message)
        self.status_label.setText(f"状态: 错误 - {error_message}")
        self.transcribe_button.setEnabled(True)

    def _save_output_file(self, file_type):
        temp_file_path = self.temp_files.get(file_type)
        if not temp_file_path or not os.path.exists(temp_file_path):
            QMessageBox.warning(self, "错误", f"未找到临时 {file_type.upper()} 文件。请先执行转录。")
            return

        original_audio_path = self.audio_path_edit.text()
        base_name = "transcription"
        if original_audio_path:
            base_name = os.path.splitext(os.path.basename(original_audio_path))[0]
        
        default_filename = f"{base_name}.{file_type}"
        filter_str = f"{file_type.upper()} 文件 (*.{file_type}) ;; 所有文件 (*)"

        save_path, _ = QFileDialog.getSaveFileName(self, f"保存 {file_type.upper()} 文件", default_filename, filter_str)

        if save_path:
            try:
                with open(temp_file_path, 'r', encoding='utf-8') as src_file:
                    with open(save_path, 'w', encoding='utf-8') as dst_file:
                        dst_file.write(src_file.read())
                QMessageBox.information(self, "成功", f"{file_type.upper()} 文件已保存到: {save_path}")
            except Exception as e:
                QMessageBox.critical(self, "保存错误", f"无法保存文件: {e}")

    def closeEvent(self, event):
        # 退出时清理临时文件
        for temp_path in self.temp_files.values():
            if temp_path and os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                    print(f"已删除临时文件: {temp_path}")
                except Exception as e:
                    print(f"删除临时文件 {temp_path} 失败: {e}")
        super().closeEvent(event)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    if not FASTER_WHISPER_AVAILABLE:
        # 如果库缺失，在创建主窗口之前显示消息框
        msg_box = QMessageBox()
        msg_box.setIcon(QMessageBox.Icon.Critical)
        msg_box.setWindowTitle("依赖缺失")
        msg_box.setText("错误: faster-whisper 库未找到或无法导入。\n请确保已正确安装 (pip install faster-whisper)。\n应用程序可能无法正常工作。")
        msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
        msg_box.exec()
        # 如果你想完全阻止应用程序启动，可以在这里选择性地使用sys.exit(1)

    gui = TranscriptionAppGUI()
    gui.show()
    sys.exit(app.exec())
