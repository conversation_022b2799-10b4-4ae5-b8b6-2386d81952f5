# 🚀 Faster-Whisper 语音转录模块

## 📋 简介

这是 LightASR 项目的 Faster-Whisper 实现模块，基于 [faster-whisper](https://github.com/systran/faster-whisper) 库开发，提供高性能的语音转文字功能。相比原始 Whisper，Faster-Whisper 使用 CTranslate2 引擎进行了优化，处理速度提升 4-5 倍，且内存占用更低。

## ✨ 主要特性

- 🖥️ **桌面GUI界面**：基于 PySide6 开发的用户友好界面
- 🌐 **网页界面**：基于 Gradio 开发的网页应用
- ⚙️ **模型管理**：支持模型下载、验证和管理
- 🔍 **词语级时间戳**：提取音频中每个词的精确时间位置
- 📝 **句级时间戳**：提取音频中每个句子的时间范围
- 📄 **导出格式**：支持 TXT 和 SRT 格式导出
- 🌍 **多语言支持**：自动检测语言或指定语言
- 🧠 **设备选择**：支持 CPU 和 CUDA（GPU）运行
- 🔢 **精度选择**：支持多种计算精度选项

## 🗂️ 文件说明

- **gui_transcribe.py**：桌面GUI转录应用，提供完整的转录功能
- **gui_model_manager.py**：模型管理界面，用于下载和管理模型
- **webui_transcribe.py**：基于Gradio的网页转录界面

## 🔧 使用方法

### 桌面GUI

1. 启动模型管理器（如需下载模型）：
   ```bash
   python gui_model_manager.py
   ```

2. 启动转录界面：
   ```bash
   python gui_transcribe.py
   ```

### 网页界面

启动网页转录界面：
```bash
python webui_transcribe.py
```

## 📦 默认模型位置

本模块默认从以下位置加载模型：
```
/home/<USER>/Model/ASR/faster_whisper_models
```

您可以在代码中修改 `CUSTOM_MODELS_BASE_DIR` 变量来自定义模型存储位置。

## 🔄 转录流程

1. 选择一个已下载的模型
2. 选择设备类型（CPU/CUDA）和计算精度
3. 选择语言（可选）
4. 上传或录制音频文件
5. 选择词语级或句级时间戳模式
6. 开始转录
7. 查看结果并导出为TXT或SRT文件

## 💡 优势

- 比原始Whisper快4-5倍
- 内存占用更低
- 提供完整的用户界面
- 支持模型管理
- 高度可配置

## 📚 依赖

- faster-whisper
- PySide6（GUI界面）
- gradio（网页界面）
- pandas（数据处理）
- torch/torchaudio（可选，用于CUDA支持）

## 🌐 相关链接

- [Faster-Whisper GitHub](https://github.com/systran/faster-whisper)
- [OpenAI Whisper](https://github.com/openai/whisper)
