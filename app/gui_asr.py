import sys
import os
import configparser
import re
import platform
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QLineEdit, QRadioButton, QButtonGroup, QCheckBox, QSpinBox,
    QGroupBox, QComboBox, QTextEdit, QFileDialog, QFormLayout
)
from PySide6.QtCore import QThread, Signal
import time
import funasr

class AsrGui(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("LightTTS语音识别客户端")
        self.setGeometry(100, 100, 900, 600)

        # 初始化主窗口组件
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        # 创建主布局（左右分栏）
        self.main_layout = QHBoxLayout(self.central_widget)
        self.main_layout.setContentsMargins(10, 10, 10, 10)  # 设置外边距
        self.main_layout.setSpacing(15)  # 左右面板之间的间距

        # 创建左右面板
        self.left_panel = QWidget()
        self.right_panel = QWidget()
        
        # 设置左右面板的布局
        self.left_layout = QVBoxLayout(self.left_panel)
        self.left_layout.setContentsMargins(5, 10, 5, 10)  # 减少左右边距
        self.right_layout = QVBoxLayout(self.right_panel)
        
        # 将左右面板添加到主布局
        self.main_layout.addWidget(self.left_panel, 40)  # 左侧面板占据40%宽度
        self.main_layout.addWidget(self.right_panel, 60)  # 右侧面板占据60%宽度

        # 模型缓存和配置数据存储
        self.asr_models_config = {}
        self.vad_models_config = {}
        self.punc_models_config = {}
        self.spk_models_config = {}
        self.model_cache = {}

        # --- 左侧控制面板组件构建 ---
        # 标题和描述
        self.title_label = QLabel("语音识别 (带时间戳和说话人)")
        self.title_label.setStyleSheet("font-size: 16pt; font-weight: bold;")
        
        self.description_label = QLabel("上传音频文件，选择运行设备和模型进行识别。可选择输出 SRT 格式，并可指定说话人数量。")
        self.description_label.setWordWrap(True)
        
        # 将标题和描述添加到左侧面板
        self.left_layout.addWidget(self.title_label)
        self.left_layout.addWidget(self.description_label)
        self.left_layout.addSpacing(15)

        # 音频文件选择
        self.audio_path_edit = QLineEdit()
        self.audio_path_edit.setPlaceholderText("请选择音频文件路径")
        self.audio_path_edit.setReadOnly(True)
        
        self.browse_button = QPushButton("浏览文件")
        self.browse_button.clicked.connect(self.browse_audio_file)
        
        # 创建文件选择布局
        audio_input_layout = QHBoxLayout()
        audio_input_layout.addWidget(self.audio_path_edit)
        audio_input_layout.addWidget(self.browse_button)
        self.left_layout.addLayout(audio_input_layout)
        self.left_layout.addSpacing(15)

        # 基础配置区域（模型类型和设备选择）
        basic_config_layout = QVBoxLayout()
        
        # 第一行：模型类型选择
        model_type_layout = QHBoxLayout()
        self.model_type_label = QLabel("模型类型:")
        self.model_type_combo = QComboBox()
        self.model_type_combo.addItem("Paraformer-zh-spk", "paraformer")
        self.model_type_combo.addItem("Seaco Paraformer", "seaco")
        self.model_type_combo.addItem("SenseVoice", "sensevoice")
        self.model_type_combo.setCurrentIndex(0)  # 默认选择第一个
        self.model_type_combo.currentTextChanged.connect(self.on_model_type_changed)
        
        model_type_layout.addWidget(self.model_type_label)
        model_type_layout.addWidget(self.model_type_combo)
        model_type_layout.addStretch()
        
        # 第二行：设备选择
        device_layout = QHBoxLayout()
        self.device_label = QLabel("运行设备:")
        self.device_combo = QComboBox()
        self.device_combo.addItem("CPU", "cpu")
        self.device_combo.addItem("CUDA", "cuda")
        
        # 根据操作系统设置默认设备
        is_mac = platform.system().lower() == 'darwin'
        default_device_index = 0 if is_mac else 1  # Mac默认CPU(0)，其他系统默认CUDA(1)
        self.device_combo.setCurrentIndex(default_device_index)
        
        device_layout.addWidget(self.device_label)
        device_layout.addWidget(self.device_combo)
        device_layout.addStretch()
        
        basic_config_layout.addLayout(model_type_layout)
        basic_config_layout.addLayout(device_layout)
        self.left_layout.addLayout(basic_config_layout)
        self.left_layout.addSpacing(10)

        # 热词输入 (仅 Seaco Paraformer 显示)
        self.hotword_label = QLabel("热词 (空格分隔):")
        self.hotword_edit = QLineEdit()
        self.hotword_edit.setPlaceholderText("请输入热词，使用空格分隔")
        self.left_layout.addWidget(self.hotword_label)
        self.left_layout.addWidget(self.hotword_edit)
        self.left_layout.addSpacing(10)

        # 输出模式和说话人选项
        options_layout = QVBoxLayout()  # 改为垂直布局以容纳更多选项
        
        # 第一行：输出模式
        output_mode_layout = QHBoxLayout()
        self.output_mode_label = QLabel("输出模式:")
        self.output_mode_combo = QComboBox()
        self.output_mode_combo.addItem("时间戳格式", "timestamp")  # 更清晰的标签
        self.output_mode_combo.addItem("普通文本", "normal")
        self.output_mode_combo.setCurrentIndex(0)  # 默认选择timestamp
        
        output_mode_layout.addWidget(self.output_mode_label)
        output_mode_layout.addWidget(self.output_mode_combo)
        output_mode_layout.addStretch()
        
        # 第二行：说话人识别选项
        speaker_layout = QHBoxLayout()
        self.speaker_enable_checkbox = QCheckBox("启用说话人识别")
        self.speaker_enable_checkbox.setChecked(True)  # 默认启用
        self.speaker_enable_checkbox.toggled.connect(self.on_speaker_enable_changed)
        
        self.speaker_count_spinbox = QSpinBox()
        self.speaker_count_spinbox.setMinimum(0)
        self.speaker_count_spinbox.setValue(0)
        self.speaker_count_spinbox.setPrefix("说话人数 (0=自动): ")
        
        speaker_layout.addWidget(self.speaker_enable_checkbox)
        speaker_layout.addSpacing(10)
        speaker_layout.addWidget(self.speaker_count_spinbox)
        speaker_layout.addStretch()
        
        options_layout.addLayout(output_mode_layout)
        options_layout.addLayout(speaker_layout)
        self.left_layout.addLayout(options_layout)
        self.left_layout.addSpacing(10)

        # 模型选择
        self.model_config_group = QGroupBox("模型配置")
        model_config_layout = QFormLayout(self.model_config_group)
        model_config_layout.setVerticalSpacing(15)  # 增加垂直间距
        model_config_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.AllNonFixedFieldsGrow)  # 允许字段增长
        
        # 设置模型选择下拉框
        self.asr_model_combo = QComboBox()
        self.asr_model_combo.setSizeAdjustPolicy(QComboBox.SizeAdjustPolicy.AdjustToContents)  # 根据内容调整大小
        self.asr_model_combo.setMinimumContentsLength(20)  # 设置最小显示长度
        
        self.vad_model_combo = QComboBox()
        self.vad_model_combo.setSizeAdjustPolicy(QComboBox.SizeAdjustPolicy.AdjustToContents)
        self.vad_model_combo.setMinimumContentsLength(20)
        
        self.punc_model_combo = QComboBox()
        self.punc_model_combo.setSizeAdjustPolicy(QComboBox.SizeAdjustPolicy.AdjustToContents)
        self.punc_model_combo.setMinimumContentsLength(20)
        
        self.spk_model_combo = QComboBox()
        self.spk_model_combo.setSizeAdjustPolicy(QComboBox.SizeAdjustPolicy.AdjustToContents)
        self.spk_model_combo.setMinimumContentsLength(20)
        
        model_config_layout.addRow("ASR 模型:", self.asr_model_combo)
        model_config_layout.addRow("VAD 模型:", self.vad_model_combo)
        model_config_layout.addRow("标点模型:", self.punc_model_combo)
        model_config_layout.addRow("说话人模型:", self.spk_model_combo)
        
        self.model_config_group.setMinimumHeight(200)  # 设置最小高度确保显示所有选项
        self.left_layout.addWidget(self.model_config_group)
        self.left_layout.addSpacing(20)

        # 识别按钮
        self.submit_button = QPushButton("开始识别")
        self.submit_button.setFixedHeight(40)
        self.submit_button.setStyleSheet("font-size: 12pt;")
        self.submit_button.clicked.connect(self.start_recognition)
        self.left_layout.addWidget(self.submit_button)
        self.left_layout.addStretch(1)  # 在底部添加可伸展空间

        # --- 右侧显示面板 ---
        # 程序状态区域
        status_header_layout = QHBoxLayout()
        self.status_label = QLabel("程序状态")
        self.status_label.setStyleSheet("font-size: 14pt; font-weight: bold;")
        
        self.clear_status_button = QPushButton("清空状态")
        self.clear_status_button.setMaximumWidth(80)
        self.clear_status_button.clicked.connect(lambda: self.status_text_edit.clear())
        
        status_header_layout.addWidget(self.status_label)
        status_header_layout.addStretch()
        status_header_layout.addWidget(self.clear_status_button)
        
        self.status_text_edit = QTextEdit()
        self.status_text_edit.setReadOnly(True)
        self.status_text_edit.setPlaceholderText("程序状态信息将显示在这里...")
        self.status_text_edit.setMaximumHeight(200)  # 限制状态区域高度
        
        # 识别结果区域
        result_header_layout = QHBoxLayout()
        self.result_label = QLabel("识别结果")
        self.result_label.setStyleSheet("font-size: 14pt; font-weight: bold;")
        
        self.download_result_button = QPushButton("下载结果")
        self.download_result_button.setMaximumWidth(80)
        self.download_result_button.setEnabled(False)  # 初始禁用，有结果后启用
        self.download_result_button.clicked.connect(self.download_recognition_result)
        
        result_header_layout.addWidget(self.result_label)
        result_header_layout.addStretch()
        result_header_layout.addWidget(self.download_result_button)
        
        self.result_text_edit = QTextEdit()
        self.result_text_edit.setReadOnly(True)
        self.result_text_edit.setPlaceholderText("识别结果将显示在这里...")
        
        # 添加到右侧面板
        self.right_layout.addLayout(status_header_layout)
        self.right_layout.addWidget(self.status_text_edit)
        self.right_layout.addSpacing(10)  # 增加间距
        self.right_layout.addLayout(result_header_layout)
        self.right_layout.addWidget(self.result_text_edit)
        
        # 设置左侧面板的尺寸限制
        from PySide6.QtWidgets import QSizePolicy
        self.left_panel.setMinimumWidth(350)  # 增加控制面板最小宽度
        self.left_panel.setMaximumWidth(500)  # 增加控制面板最大宽度
        
        # 右侧面板应可伸展
        self.right_panel.setSizePolicy(
            QSizePolicy.Policy.Expanding,
            QSizePolicy.Policy.Expanding
        )
        
        # 存储当前识别结果的信息，用于下载功能
        self.current_recognition_result = ""
        self.current_audio_filename = ""
        self.current_output_mode = "timestamp"

        # 初始化界面状态（包含模型配置加载）
        self.on_model_type_changed()

    def browse_audio_file(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "选择音频文件", "", "音频文件 (*.wav *.mp3 *.flac *.m4a *.pcm)")
        if file_path:
            self.audio_path_edit.setText(file_path)
            self.status_text_edit.append(f"已选择音频文件: {file_path}")

    def get_current_model_type(self):
        """获取当前选择的模型类型"""
        return self.model_type_combo.currentData()

    def on_speaker_enable_changed(self):
        """说话人识别启用状态改变时更新界面"""
        enabled = self.speaker_enable_checkbox.isChecked()
        self.speaker_count_spinbox.setVisible(enabled)
        
        # 根据当前模型类型和说话人启用状态更新说话人模型下拉框的可见性
        model_type = self.get_current_model_type()
        if model_type in ["paraformer", "seaco"]:
            # 只有在启用说话人识别时才显示说话人模型选择
            # 但标点模型不受影响
            pass  # 说话人模型的显示/隐藏在 on_model_type_changed 中处理

    def on_model_type_changed(self):
        """模型类型改变时更新界面"""
        model_type = self.get_current_model_type()
        
        # 根据模型类型设置界面显示
        ui_config = {
            "paraformer": {
                "title": "Paraformer 语音识别 \n(带时间戳和说话人)",
                "description": "上传音频文件，选择运行设备和模型进行识别。可选择输出格式，并可启用说话人识别。",
                "show_hotword": False,
                "show_speaker": True,
                "show_punc_spk": True
            },
            "seaco": {
                "title": "Seaco Paraformer 语音识别 \n(带时间戳、说话人和热词)",
                "description": "上传音频文件，选择运行设备和模型进行识别。支持自定义热词、多种输出格式和说话人识别。",
                "show_hotword": True,
                "show_speaker": True,
                "show_punc_spk": True
            },
            "sensevoice": {
                "title": "SenseVoice 语音识别 \n(带时间戳和情感识别)",
                "description": "上传音频文件，选择运行设备和模型进行识别。支持情感标签识别、事件检测和多种输出格式。",
                "show_hotword": False,
                "show_speaker": False,
                "show_punc_spk": False
            }
        }
        
        config = ui_config[model_type]
        
        # 更新标题和描述
        self.title_label.setText(config["title"])
        self.description_label.setText(config["description"])
        
        # 显示/隐藏组件
        self.hotword_label.setVisible(config["show_hotword"])
        self.hotword_edit.setVisible(config["show_hotword"])
        
        # 说话人相关组件
        self.speaker_enable_checkbox.setVisible(config["show_speaker"])
        speaker_enabled = self.speaker_enable_checkbox.isChecked() and config["show_speaker"]
        self.speaker_count_spinbox.setVisible(speaker_enabled)
        
        # 模型选择组件
        self.punc_model_combo.setVisible(config["show_punc_spk"])
        self.spk_model_combo.setVisible(config["show_punc_spk"])
        
        # 重新加载模型配置
        self.load_model_config()

    def load_model_config(self):
        # 统一的配置文件路径
        config_paths = [
            # os.path.join(os.getcwd(), "app", "model.conf"),
            os.path.join(os.getcwd(), "app", "model_mac.conf")
        ]
        
        config = configparser.ConfigParser()
        loaded_path = None
        for path in config_paths:
            if os.path.exists(path):
                try:
                    config.read(path, encoding='utf-8')
                    loaded_path = path
                    break
                except Exception as e:
                    self.status_text_edit.append(f"读取配置文件 {path} 失败: {e}")
                    return

        if not loaded_path:
            self.status_text_edit.append("错误: 未找到 model.conf 文件。请确保它位于项目根目录或 app 目录下。")
            self.asr_model_combo.addItem("错误: 未找到 model.conf")
            self.vad_model_combo.addItem("错误: 未找到 model.conf")
            self.punc_model_combo.addItem("错误: 未找到 model.conf")
            self.spk_model_combo.addItem("错误: 未找到 model.conf")
            return

        self.status_text_edit.append(f"成功从 {loaded_path} 加载模型配置。")

        def populate_combo(combo, section_name, model_dict):
            combo.clear()
            model_dict.clear()
            if section_name in config:
                for name, path_or_id in config[section_name].items():
                    combo.addItem(name)
                    model_dict[name] = path_or_id
                if not model_dict:
                     combo.addItem(f"无模型 (在 {section_name})")
            else:
                self.status_text_edit.append(f"警告: 配置文件中未找到 [{section_name}] 部分。")
                combo.addItem(f"无模型 (无 {section_name})")

        model_type = self.get_current_model_type()
        
        # 根据模型类型选择对应的ASR模型section
        if model_type == "paraformer":
            populate_combo(self.asr_model_combo, "asr_models_dir", self.asr_models_config)
        elif model_type == "seaco":
            populate_combo(self.asr_model_combo, "asr_seaco_models_dir", self.asr_models_config)
        elif model_type == "sensevoice":
            populate_combo(self.asr_model_combo, "asr_sense_model_dir", self.asr_models_config)
        
        # VAD模型对所有类型都需要
        populate_combo(self.vad_model_combo, "vad_models_dir", self.vad_models_config)
        
        # 根据模型类型决定是否加载其他模型
        if model_type in ["paraformer", "seaco"]:
            populate_combo(self.punc_model_combo, "punc_models_dir", self.punc_models_config)
            populate_combo(self.spk_model_combo, "spk_models_dir", self.spk_models_config)
            
            # 为可选模型添加"不使用"选项，但默认选择第一个实际模型
            for combo, model_cfg in [(self.vad_model_combo, self.vad_models_config), 
                                     (self.punc_model_combo, self.punc_models_config), 
                                     (self.spk_model_combo, self.spk_models_config)]:
                if combo.count() > 0 and "无模型" not in combo.itemText(0) and "错误:" not in combo.itemText(0):
                    combo.insertItem(0, "None (不使用)")
                    model_cfg["None (不使用)"] = None 
                    # 默认选择第1个实际模型，而不是"None (不使用)"
                    if combo.count() > 1:
                        combo.setCurrentIndex(1)
        elif model_type == "sensevoice":
            # SenseVoice 只需要 ASR 和 VAD 模型，清空其他模型配置
            self.punc_models_config.clear()
            self.spk_models_config.clear()
            self.punc_model_combo.clear()
            self.spk_model_combo.clear()
            
            # VAD 模型添加不使用选项
            if self.vad_model_combo.count() > 0 and "无模型" not in self.vad_model_combo.itemText(0) and "错误:" not in self.vad_model_combo.itemText(0):
                self.vad_model_combo.insertItem(0, "None (不使用)")
                self.vad_models_config["None (不使用)"] = None 
                if self.vad_model_combo.count() > 1:
                    self.vad_model_combo.setCurrentIndex(1)

    def start_recognition(self):
        audio_file = self.audio_path_edit.text()
        if not audio_file or not os.path.exists(audio_file):
            self.status_text_edit.append("错误: 请先选择一个有效的音频文件。")
            return

        device = self.device_combo.currentData()
        
        asr_model_name = self.asr_model_combo.currentText()
        vad_model_name = self.vad_model_combo.currentText()
        punc_model_name = self.punc_model_combo.currentText()
        spk_model_name = self.spk_model_combo.currentText()

        asr_model_path = self.asr_models_config.get(asr_model_name)
        vad_model_path = self.vad_models_config.get(vad_model_name)
        punc_model_path = self.punc_models_config.get(punc_model_name)
        spk_model_path = self.spk_models_config.get(spk_model_name)

        if not asr_model_path or "错误:" in asr_model_name or "无模型" in asr_model_name :
            self.status_text_edit.append("错误: 请选择一个有效的 ASR 模型。")
            return
        
        if "错误:" in vad_model_name or "无模型" in vad_model_name or vad_model_name == "None (不使用)": vad_model_path = None
        if "错误:" in punc_model_name or "无模型" in punc_model_name or punc_model_name == "None (不使用)": punc_model_path = None
        if "错误:" in spk_model_name or "无模型" in spk_model_name or spk_model_name == "None (不使用)": spk_model_path = None

        output_mode = self.output_mode_combo.currentData()
        
        # 说话人识别相关参数
        speaker_enabled = self.speaker_enable_checkbox.isChecked() and self.speaker_enable_checkbox.isVisible()
        num_speakers = self.speaker_count_spinbox.value() if speaker_enabled else 0
        
        # 如果禁用说话人识别，强制设置说话人模型路径为 None
        if not speaker_enabled:
            spk_model_path = None
            
        hotword = self.hotword_edit.text().strip() if self.hotword_edit.isVisible() else ""
        model_type = self.get_current_model_type()

        # 清空结果区域，准备显示新的识别结果
        self.result_text_edit.clear()
        self.result_label.setText("识别结果")
        self.download_result_button.setEnabled(False)  # 禁用下载按钮
        
        # 保存当前识别参数用于下载
        self.current_audio_filename = os.path.splitext(os.path.basename(audio_file))[0]
        self.current_output_mode = output_mode
        
        # 状态信息输出到状态区域
        self.status_text_edit.append(f"开始识别: {os.path.basename(audio_file)}")
        self.status_text_edit.append(f"  模型类型: {self.model_type_combo.currentText()}")
        self.status_text_edit.append(f"  设备: {self.device_combo.currentText()}")
        self.status_text_edit.append(f"  ASR 模型: {asr_model_name}")
        self.status_text_edit.append(f"  VAD 模型: {vad_model_name if vad_model_path else '不使用'}")
        
        if model_type in ["paraformer", "seaco"]:
            self.status_text_edit.append(f"  标点模型: {punc_model_name if punc_model_path else '不使用'}")
            if speaker_enabled:
                self.status_text_edit.append(f"  说话人模型: {spk_model_name if spk_model_path else '不使用'}")
                self.status_text_edit.append(f"  说话人数: {num_speakers if num_speakers > 0 else '自动'}")
            else:
                self.status_text_edit.append(f"  说话人识别: 已禁用")
            
        if model_type == "seaco" and hotword:
            self.status_text_edit.append(f"  热词: {hotword}")
            
        self.status_text_edit.append(f"  输出模式: {'时间戳格式' if output_mode == 'timestamp' else '普通文本'}")
        self.status_text_edit.append("")

        self.submit_button.setEnabled(False)
        self.submit_button.setText("正在识别中...")

        self.recognition_thread = RecognitionWorker(
            audio_file, device, 
            asr_model_path, vad_model_path, punc_model_path, spk_model_path,
            output_mode, num_speakers, hotword, model_type, self.model_cache, speaker_enabled
        )
        self.recognition_thread.progress.connect(self.update_progress)
        self.recognition_thread.finished.connect(self.recognition_finished)
        self.recognition_thread.error.connect(self.recognition_error)
        self.recognition_thread.start()

    def update_progress(self, message):
        self.status_text_edit.append(message)

    def recognition_finished(self, result_text, processing_time):
        # 状态信息更新
        self.status_text_edit.append(f"识别完成，耗时: {processing_time:.2f} 秒")
        
        # 识别结果显示
        self.result_text_edit.clear()
        self.result_label.setText(f"识别结果 (耗时: {processing_time:.2f} 秒)")
        self.result_text_edit.append(result_text)
        
        # 保存识别结果并启用下载按钮
        self.current_recognition_result = result_text
        self.download_result_button.setEnabled(True)
        
        self.submit_button.setEnabled(True)
        self.submit_button.setText("开始识别")

    def recognition_error(self, error_message):
        self.status_text_edit.append(f"--- 错误 ---")
        self.status_text_edit.append(error_message)
        self.submit_button.setEnabled(True)
        self.submit_button.setText("开始识别")

    def download_recognition_result(self):
        """下载识别结果到文件"""
        if not self.current_recognition_result:
            self.status_text_edit.append("错误: 没有可下载的识别结果。")
            return
        
        # 根据输出格式确定文件扩展名和过滤器
        if self.current_output_mode == "timestamp":
            default_filename = f"{self.current_audio_filename}.srt"
            file_filter = "SRT字幕文件 (*.srt);;所有文件 (*)"
        else:
            default_filename = f"{self.current_audio_filename}.txt"
            file_filter = "文本文件 (*.txt);;所有文件 (*)"
        
        # 打开保存对话框
        file_path, _ = QFileDialog.getSaveFileName(
            self, 
            "保存识别结果", 
            default_filename, 
            file_filter
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.current_recognition_result)
                self.status_text_edit.append(f"识别结果已保存到: {file_path}")
            except Exception as e:
                self.status_text_edit.append(f"保存文件失败: {str(e)}")

class RecognitionWorker(QThread):
    progress = Signal(str)
    finished = Signal(str, float)  # Added float for processing time
    error = Signal(str)

    def __init__(self, audio_file, device, 
                 asr_model_path, vad_model_path, punc_model_path, spk_model_path,
                 output_mode, num_speakers, hotword, model_type, model_cache_dict, speaker_enabled):
        super().__init__()
        self.audio_file = audio_file
        self.device = device
        self.asr_model_path = asr_model_path
        self.vad_model_path = vad_model_path
        self.punc_model_path = punc_model_path
        self.spk_model_path = spk_model_path
        self.output_mode = output_mode
        self.num_speakers = num_speakers
        self.hotword = hotword
        self.model_type = model_type
        self.model_cache = model_cache_dict
        self.speaker_enabled = speaker_enabled

    def _format_timestamp(self, milliseconds, srt_format=False):
        """Converts milliseconds to HH:MM:SS,ms or HH:MM:SS.ms format."""
        seconds = milliseconds // 1000
        ms = milliseconds % 1000
        minutes = seconds // 60
        seconds %= 60
        hours = minutes // 60
        minutes %= 60
        if srt_format:
            return f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d},{int(ms):03d}"
        else:
            return f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}.{int(ms):03d}"

    def run(self):
        try:
            self.progress.emit("正在准备模型...")
            model_key_parts = [
                self.device,
                self.asr_model_path,
                str(self.vad_model_path),
                str(self.punc_model_path),
                str(self.spk_model_path),
                self.model_type
            ]
            model_key = "_".join(filter(None, model_key_parts))

            if model_key in self.model_cache:
                model = self.model_cache[model_key]
                self.progress.emit(f"从缓存加载模型")
            else:
                self.progress.emit(f"首次加载模型 (可能需要一些时间)")
                model_kwargs = {
                    "model": self.asr_model_path,
                    "device": self.device,
                    "disable_update": True
                }
                
                # 根据模型类型设置不同的参数
                if self.model_type == "paraformer":
                    model_kwargs["model_revision"] = "master"
                    if self.vad_model_path:
                        model_kwargs["vad_model"] = self.vad_model_path
                    if self.punc_model_path:
                        model_kwargs["punc_model"] = self.punc_model_path
                    if self.spk_model_path and self.speaker_enabled:
                        model_kwargs["spk_model"] = self.spk_model_path
                        model_kwargs["timestamp"] = True
                        
                elif self.model_type == "seaco":
                    model_kwargs["model_revision"] = "master"
                    if self.vad_model_path:
                        model_kwargs["vad_model"] = self.vad_model_path
                        model_kwargs["vad_kwargs"] = {"max_single_segment_time": 60000}
                    if self.punc_model_path:
                        model_kwargs["punc_model"] = self.punc_model_path
                    if self.spk_model_path and self.speaker_enabled:
                        model_kwargs["spk_model"] = self.spk_model_path
                        model_kwargs["timestamp"] = True
                        
                elif self.model_type == "sensevoice":
                    if self.vad_model_path:
                        model_kwargs["vad_model"] = self.vad_model_path
                        model_kwargs["vad_kwargs"] = {"max_single_segment_time": 30000}
                
                model = funasr.AutoModel(**model_kwargs)
                self.model_cache[model_key] = model
                self.progress.emit("模型加载完成.")

            self.progress.emit(f"开始识别音频文件: {os.path.basename(self.audio_file)}")
            
            # 根据模型类型设置不同的识别参数
            generate_kwargs = {"input": self.audio_file}
            
            if self.model_type in ["paraformer", "seaco"]:
                generate_kwargs["cache"] = {}
                generate_kwargs["return_raw_text"] = True
                if self.spk_model_path and self.speaker_enabled and self.num_speakers > 0:
                    generate_kwargs['preset_spk_num'] = self.num_speakers
                if self.model_type == "seaco" and self.hotword:
                    generate_kwargs['hotword'] = self.hotword
                    
            elif self.model_type == "sensevoice":
                generate_kwargs.update({
                    "cache": {},
                    "language": "auto",
                    "batch_size_s": 60,
                    "merge_vad": True,
                    "merge_length_s": 15,
                    "output_timestamp": self.output_mode == "timestamp"
                })

            start_process_time = time.time()
            rec_result = model.generate(**generate_kwargs)
            end_process_time = time.time()
            processing_time = end_process_time - start_process_time
            self.progress.emit("原始识别结果获取完毕, 正在格式化...")
            
            # 根据模型类型处理结果
            if self.model_type == "sensevoice":
                output_lines = self._process_sensevoice_result(rec_result)
            else:
                output_lines = self._process_paraformer_result(rec_result)
            
            self.finished.emit("\n".join(output_lines), processing_time)

        except Exception as e:
            self.error.emit(f"识别过程中发生错误: {str(e)}\n请检查模型路径、音频文件格式和依赖项是否正确安装。\n错误详情: {type(e).__name__}: {e}")

    def _process_paraformer_result(self, rec_result):
        """处理 Paraformer 和 Seaco Paraformer 的识别结果"""
        
        output_lines = []
        if rec_result:
            subtitle_index = 1
            for result in rec_result:
                if 'sentence_info' in result:
                    for sentence in result['sentence_info']:
                        spk_value = sentence.get('spk')
                        # 只有在启用说话人识别时才显示说话人标签
                        if self.speaker_enabled and spk_value is not None and isinstance(spk_value, int):
                            speaker = f"spk{spk_value + 1}"
                            speaker_formatted = f"[{speaker}]   "
                        else:
                            speaker_formatted = ""
                            
                        text = sentence.get('text', '').strip().rstrip(',.。，!！?？')
                        start_time = self._format_timestamp(sentence.get('start', 0), srt_format=(self.output_mode == "timestamp"))
                        end_time = self._format_timestamp(sentence.get('end', 0), srt_format=(self.output_mode == "timestamp"))
                        
                        if self.output_mode == "timestamp":
                            output_lines.append(str(subtitle_index))
                            output_lines.append(f"{start_time} --> {end_time}")
                            output_lines.append(f"{speaker_formatted}{text}")
                            output_lines.append("")
                            subtitle_index += 1
                        else:
                            output_lines.append(f"{speaker_formatted}{text}")
                elif 'text' in result:
                    output_lines.append(result.get('text', ''))
        else:
            output_lines.append("识别结果为空。")
        
        return output_lines

    def _process_sensevoice_result(self, rec_result):
        """处理 SenseVoice 的识别结果"""
        
        output_lines = []
        
        # 普通模式
        if self.output_mode == "normal":
            texts = [r.get('text', '').strip() for r in rec_result if r.get('text')]
            return texts
        
        # 时间戳模式，构建 SRT 样式
        if rec_result:
            raw = rec_result[0].get('text', '')
            timestamps = rec_result[0].get('timestamp', [])
            
            # 情感和事件标签
            emo_tags = {'<|HAPPY|>', '<|SAD|>', '<|ANGRY|>', '<|NEUTRAL|>', '<|FEARFUL|>', '<|DISGUSTED|>', '<|SURPRISED|>'}
            event_tags = {'<|Speech|>', '<|BGM|>', '<|Applause|>', '<|Laughter|>', '<|Cry|>', '<|Sneeze|>', '<|Breath|>', '<|Cough|>', '<|Event_UNK|>'}
            
            parts = raw.split('<|zh|>')
            offset = 0
            segments = []
            
            for part in parts:
                if not part:
                    continue
                m = re.match(r'((?:<\|[^|]+\|>)+)', part)
                if m:
                    tags = ''.join([t for t in re.findall(r'<\|[^|]+\|>', m.group(1)) if t in emo_tags or t in event_tags])
                    content = part[len(m.group(1)):].strip()
                else:
                    tags = ''
                    content = part.strip()
                
                length = len(content)
                if length == 0:
                    continue
                
                st = timestamps[offset][0] if offset < len(timestamps) else 0
                et = timestamps[offset + length - 1][1] if offset + length - 1 < len(timestamps) else st
                segments.append((tags + content, st, et))
                offset += length
            
            # 格式化输出
            for idx, (txt, st, et) in enumerate(segments, 1):
                output_lines.append(str(idx))
                output_lines.append(f"{self._ms2srt(st)} --> {self._ms2srt(et)}")
                output_lines.append(txt)
                output_lines.append("")
        
        return output_lines if output_lines else ["识别结果为空。"]

    def _ms2srt(self, ms):
        """SenseVoice 时间戳格式化函数"""
        h = ms // 3600000
        m = (ms % 3600000) // 60000
        s = (ms % 60000) // 1000
        ms_r = ms % 1000
        return f"{h:02d}:{m:02d}:{s:02d},{ms_r:03d}"

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = AsrGui()
    window.show()
    sys.exit(app.exec())
