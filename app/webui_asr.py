import gradio as gr
from funasr import AutoModel
import os
import configparser
import time
import re
import platform

# 时间戳格式化函数
def format_timestamp(ms, srt_format=False):
    """将毫秒转换为时间戳格式"""
    seconds = int(ms / 1000)
    milliseconds = int(ms % 1000)
    hours = seconds // 3600
    seconds %= 3600
    minutes = seconds // 60
    seconds %= 60
    if srt_format:
        return f"{hours:02d}:{minutes:02d}:{seconds:02d},{milliseconds:03d}"
    else:
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}.{milliseconds:03d}"

def ms2srt(ms):
    """SenseVoice时间戳格式化函数"""
    h = ms // 3600000
    m = (ms % 3600000) // 60000
    s = (ms % 60000) // 1000
    ms_r = ms % 1000
    return f"{h:02d}:{m:02d}:{s:02d},{ms_r:03d}"

# 加载模型配置
config = configparser.ConfigParser()
config_paths = [
    os.path.join(os.path.dirname(os.path.abspath(__file__)), 'model_mac.conf'),
    # os.path.join(os.path.dirname(os.path.abspath(__file__)), 'model.conf')
]

loaded_config_path = None
for config_path in config_paths:
    if os.path.exists(config_path):
        try:
            config.read(config_path, encoding='utf-8')
            loaded_config_path = config_path
            break
        except Exception as e:
            print(f"读取配置文件 {config_path} 失败: {e}")

if not loaded_config_path:
    raise FileNotFoundError(f"配置文件未找到，检查路径: {config_paths}")

print(f"成功加载配置文件: {loaded_config_path}")

# 获取各种模型列表
asr_models = dict(config['asr_models_dir']) if 'asr_models_dir' in config else {}
asr_seaco_models = dict(config['asr_seaco_models_dir']) if 'asr_seaco_models_dir' in config else {}
asr_sense_models = dict(config['asr_sense_model_dir']) if 'asr_sense_model_dir' in config else {}
vad_models = dict(config['vad_models_dir']) if 'vad_models_dir' in config else {}
punc_models = dict(config['punc_models_dir']) if 'punc_models_dir' in config else {}
spk_models = dict(config['spk_models_dir']) if 'spk_models_dir' in config else {}

# 添加不使用选项
vad_models_with_none = {"None (不使用)": None}
vad_models_with_none.update(vad_models)

punc_models_with_none = {"None (不使用)": None}
punc_models_with_none.update(punc_models)

spk_models_with_none = {"None (不使用)": None}
spk_models_with_none.update(spk_models)

# 默认模型配置
default_asr = list(asr_models.keys())[0] if asr_models else None
default_asr_seaco = list(asr_seaco_models.keys())[0] if asr_seaco_models else None
default_asr_sense = list(asr_sense_models.keys())[0] if asr_sense_models else None
default_vad = list(vad_models.keys())[0] if vad_models else None
default_punc = list(punc_models.keys())[0] if punc_models else None
default_spk = list(spk_models.keys())[0] if spk_models else None

# 根据操作系统设置默认设备
is_mac = platform.system().lower() == 'darwin'
default_device = 'cpu' if is_mac else 'cuda'

# 模型实例缓存
model_cache = {}

def get_model(device, model_type, asr_model_name, vad_model_name, punc_model_name, spk_model_name, speaker_enabled):
    """获取或加载所选模型实例"""
    # 如果禁用说话人识别，强制设置说话人模型为None
    if not speaker_enabled:
        spk_model_name = "None (不使用)"
    
    key = f"{device}_{model_type}_{asr_model_name}_{vad_model_name}_{punc_model_name}_{spk_model_name}"
    if key not in model_cache:
        print(f"为配置 {key} 加载模型...")
        
        # 根据模型类型选择ASR模型路径
        if model_type == "seaco":
            asr_path = asr_seaco_models.get(asr_model_name)
        elif model_type == "sensevoice":
            asr_path = asr_sense_models.get(asr_model_name)
        else:  # paraformer
            asr_path = asr_models.get(asr_model_name)
        
        vad_path = vad_models_with_none.get(vad_model_name)
        punc_path = punc_models_with_none.get(punc_model_name)
        spk_path = spk_models_with_none.get(spk_model_name)
        
        if not asr_path:
            raise ValueError(f"ASR 模型路径缺失: {asr_model_name}")
        
        params = {
            "disable_update": True,
            "device": device,
            "model": asr_path
        }
        
        # 根据模型类型设置不同的参数
        if model_type == "paraformer":
            params["model_revision"] = "master"
            if vad_path:
                params["vad_model"] = vad_path
            if punc_path:
                params["punc_model"] = punc_path
            if spk_path and speaker_enabled:
                params["spk_model"] = spk_path
                params["timestamp"] = True
                
        elif model_type == "seaco":
            params["model_revision"] = "master"
            if vad_path:
                params["vad_model"] = vad_path
                params["vad_kwargs"] = {"max_single_segment_time": 60000}
            if punc_path:
                params["punc_model"] = punc_path
            if spk_path and speaker_enabled:
                params["spk_model"] = spk_path
                params["timestamp"] = True
                
        elif model_type == "sensevoice":
            if vad_path:
                params["vad_model"] = vad_path
                params["vad_kwargs"] = {"max_single_segment_time": 30000}
        
        model_cache[key] = AutoModel(**params)
        print(f"配置 {key} 的模型加载完成。")
    return model_cache[key]

def update_asr_models(model_type):
    """根据模型类型更新ASR模型选择"""
    if model_type == "Paraformer-zh-spk":
        choices = list(asr_models.keys())
        default = default_asr
        info = "已切换到Paraformer模型，支持时间戳和说话人识别"
    elif model_type == "Seaco Paraformer":
        choices = list(asr_seaco_models.keys())
        default = default_asr_seaco
        info = "已切换到Seaco Paraformer模型，支持热词增强、时间戳和说话人识别"
    else:  # SenseVoice
        choices = list(asr_sense_models.keys())
        default = default_asr_sense
        info = "已切换到SenseVoice模型，支持情感标签识别、事件检测和时间戳"
    
    return gr.update(choices=choices, value=default), info

def update_interface_visibility(model_type):
    """根据模型类型更新界面元素的可见性"""
    show_hotword = model_type == "Seaco Paraformer"
    show_speaker = model_type in ["Paraformer-zh-spk", "Seaco Paraformer"]
    show_punc_spk = model_type in ["Paraformer-zh-spk", "Seaco Paraformer"]
    
    hotword_update = gr.update(
        visible=show_hotword,
        interactive=show_hotword,
        placeholder="请输入热词，使用空格分隔" if show_hotword else "当前模型不支持热词功能"
    )
    
    speaker_update = gr.update(visible=show_speaker)
    speaker_num_update = gr.update(visible=show_speaker)
    punc_update = gr.update(visible=show_punc_spk)
    spk_update = gr.update(visible=show_punc_spk)
    
    return hotword_update, speaker_update, speaker_num_update, punc_update, spk_update

def update_speaker_num_visibility(speaker_enabled):
    """根据说话人启用状态显示/隐藏说话人数输入"""
    return gr.update(visible=speaker_enabled)

def process_paraformer_result(rec_result, output_mode, speaker_enabled):
    """处理 Paraformer 和 Seaco Paraformer 的识别结果"""
    output_lines = []
    if rec_result:
        subtitle_index = 1
        for result in rec_result:
            if 'sentence_info' in result:
                for sentence in result['sentence_info']:
                    spk_value = sentence.get('spk')
                    # 只有在启用说话人识别时才显示说话人标签
                    if speaker_enabled and spk_value is not None and isinstance(spk_value, int):
                        speaker = f"spk{spk_value + 1}"
                        speaker_formatted = f"[{speaker}]   "
                    else:
                        speaker_formatted = ""
                        
                    text = sentence.get('text', '').strip().rstrip(',.。，!！?？')
                    start_time = format_timestamp(sentence.get('start', 0), srt_format=(output_mode == "timestamp"))
                    end_time = format_timestamp(sentence.get('end', 0), srt_format=(output_mode == "timestamp"))
                    
                    if output_mode == "timestamp":
                        output_lines.append(str(subtitle_index))
                        output_lines.append(f"{start_time} --> {end_time}")
                        output_lines.append(f"{speaker_formatted}{text}")
                        output_lines.append("")
                        subtitle_index += 1
                    else:
                        output_lines.append(f"{speaker_formatted}{text}")
            elif 'text' in result:
                output_lines.append(result.get('text', ''))
    else:
        output_lines.append("识别结果为空。")
    
    return output_lines

def process_sensevoice_result(rec_result, output_mode):
    """处理 SenseVoice 的识别结果"""
    output_lines = []
    
    # 普通模式
    if output_mode == "normal":
        texts = [r.get('text', '').strip() for r in rec_result if r.get('text')]
        return texts
    
    # 时间戳模式，构建 SRT 样式
    if rec_result:
        raw = rec_result[0].get('text', '')
        timestamps = rec_result[0].get('timestamp', [])
        
        # 情感和事件标签
        emo_tags = {'<|HAPPY|>', '<|SAD|>', '<|ANGRY|>', '<|NEUTRAL|>', '<|FEARFUL|>', '<|DISGUSTED|>', '<|SURPRISED|>'}
        event_tags = {'<|Speech|>', '<|BGM|>', '<|Applause|>', '<|Laughter|>', '<|Cry|>', '<|Sneeze|>', '<|Breath|>', '<|Cough|>', '<|Event_UNK|>'}
        
        parts = raw.split('<|zh|>')
        offset = 0
        segments = []
        
        for part in parts:
            if not part:
                continue
            m = re.match(r'((?:<\|[^|]+\|>)+)', part)
            if m:
                tags = ''.join([t for t in re.findall(r'<\|[^|]+\|>', m.group(1)) if t in emo_tags or t in event_tags])
                content = part[len(m.group(1)):].strip()
            else:
                tags = ''
                content = part.strip()
            
            length = len(content)
            if length == 0:
                continue
            
            st = timestamps[offset][0] if offset < len(timestamps) else 0
            et = timestamps[offset + length - 1][1] if offset + length - 1 < len(timestamps) else st
            segments.append((tags + content, st, et))
            offset += length
        
        # 格式化输出
        for idx, (txt, st, et) in enumerate(segments, 1):
            output_lines.append(str(idx))
            output_lines.append(f"{ms2srt(st)} --> {ms2srt(et)}")
            output_lines.append(txt)
            output_lines.append("")
    
    return output_lines if output_lines else ["识别结果为空。"]

# 主处理函数
def process_audio(audio_path, device, model_type, asr_choice, vad_choice, punc_choice, spk_choice, 
                 hotword, output_mode, speaker_enabled, num_speakers):
    if not audio_path:
        status_info = "错误: 请先上传一个音频文件。"
        return "", status_info, gr.update(label="识别结果")
    
    if not asr_choice:
        status_info = "错误: 请选择一个ASR模型。"
        return "", status_info, gr.update(label="识别结果")
    
    # 检查必要模型
    if model_type in ["Paraformer-zh-spk", "Seaco Paraformer"]:
        if not vad_choice or "错误:" in vad_choice or "无模型" in vad_choice:
            if vad_choice != "None (不使用)":
                status_info = "错误: 请选择一个有效的VAD模型。"
                return "", status_info, gr.update(label="识别结果")
    
    # 确定模型类型标识
    model_type_map = {
        "Paraformer-zh-spk": "paraformer",
        "Seaco Paraformer": "seaco", 
        "SenseVoice": "sensevoice"
    }
    model_type_key = model_type_map[model_type]
    
    # 状态信息
    status_lines = [
        f"开始识别: {os.path.basename(audio_path)}",
        f"  模型类型: {model_type}",
        f"  设备: {device.upper()}",
        f"  ASR 模型: {asr_choice}",
    ]
    
    if model_type in ["Paraformer-zh-spk", "Seaco Paraformer"]:
        status_lines.append(f"  VAD 模型: {vad_choice if vad_choice != 'None (不使用)' else '不使用'}")
        status_lines.append(f"  标点模型: {punc_choice if punc_choice != 'None (不使用)' else '不使用'}")
        if speaker_enabled:
            status_lines.append(f"  说话人模型: {spk_choice if spk_choice != 'None (不使用)' else '不使用'}")
            status_lines.append(f"  说话人数: {num_speakers if num_speakers > 0 else '自动'}")
        else:
            status_lines.append(f"  说话人识别: 已禁用")
    elif model_type == "SenseVoice":
        status_lines.append(f"  VAD 模型: {vad_choice if vad_choice != 'None (不使用)' else '不使用'}")
    
    if model_type == "Seaco Paraformer" and hotword:
        status_lines.append(f"  热词: {hotword}")
    
    status_lines.append(f"  输出模式: {'时间戳格式' if output_mode == 'timestamp' else '普通文本'}")
    status_lines.append("")
    status_lines.append("正在准备模型...")
    
    try:
        # 获取或加载模型
        model_config_id = f"{device}_{model_type_key}_{asr_choice}_{vad_choice}_{punc_choice}_{spk_choice}_{speaker_enabled}"
        if not hasattr(process_audio, 'current_model') or process_audio.current_model != model_config_id:
            status_lines.append(f"加载新模型组合: {model_config_id}")
            process_audio.model = get_model(device, model_type_key, asr_choice, vad_choice, punc_choice, spk_choice, speaker_enabled)
            process_audio.current_model = model_config_id
        else:
            status_lines.append("从缓存加载模型")
        
        model = process_audio.model
        status_lines.append("模型加载完成.")
        status_lines.append(f"开始识别音频文件: {os.path.basename(audio_path)}")
        
        # 根据模型类型设置不同的识别参数
        generate_kwargs = {"input": audio_path}
        
        if model_type_key in ["paraformer", "seaco"]:
            generate_kwargs["cache"] = {}
            generate_kwargs["return_raw_text"] = True
            if spk_choice != "None (不使用)" and speaker_enabled and num_speakers > 0:
                generate_kwargs['preset_spk_num'] = num_speakers
            if model_type_key == "seaco" and hotword:
                generate_kwargs['hotword'] = hotword
                
        elif model_type_key == "sensevoice":
            generate_kwargs.update({
                "cache": {},
                "language": "auto",
                "batch_size_s": 60,
                "merge_vad": True,
                "merge_length_s": 15,
                "output_timestamp": output_mode == "timestamp"
            })
        
        # 记录识别开始时间
        start_process_time = time.time()
        rec_result = model.generate(**generate_kwargs)
        end_process_time = time.time()
        processing_time = end_process_time - start_process_time
        
        status_lines.append("原始识别结果获取完毕, 正在格式化...")
        
        # 根据模型类型处理结果
        if model_type_key == "sensevoice":
            output_lines = process_sensevoice_result(rec_result, output_mode)
        else:
            output_lines = process_paraformer_result(rec_result, output_mode, speaker_enabled)
        
        result_text = "\n".join(output_lines) if output_lines else "识别结果为空。"
        result_label = f"识别结果 (耗时: {processing_time:.2f} 秒)"
        
        status_lines.append(f"识别完成，耗时: {processing_time:.2f} 秒")
        status_info = "\n".join(status_lines)
        
        return result_text, status_info, gr.update(label=result_label)
        
    except Exception as e:
        error_info = f"识别过程中发生错误: {str(e)}\n请检查模型路径、音频文件格式和依赖项是否正确安装。\n错误详情: {type(e).__name__}: {e}"
        status_lines.append("--- 错误 ---")
        status_lines.append(error_info)
        status_info = "\n".join(status_lines)
        
        return "", status_info, gr.update(label="识别结果")

# Gradio 界面
with gr.Blocks(theme=gr.themes.Soft(), title="LightASR 语音识别客户端") as demo:
    gr.Markdown("# LightASR 统一语音识别")
    gr.Markdown("支持 Paraformer、Seaco Paraformer 和 SenseVoice 多种模型，提供时间戳、说话人识别、热词增强、情感检测等功能。")
    
    with gr.Row():
        with gr.Column(scale=1):
            # 音频文件上传
            audio_input = gr.Audio(type="filepath", label="上传音频文件")
            
            with gr.Row():
                # 模型类型选择
                model_type_select = gr.Radio(
                    choices=["Paraformer-zh-spk", "Seaco Paraformer", "SenseVoice"], 
                    value="Paraformer-zh-spk", 
                    label="模型类型"
                )
                
                # 设备选择
                device_select = gr.Radio(
                    choices=["cuda", "cpu"], 
                    value=default_device, 
                    label="运行设备"
                )
            
            # 热词输入 (仅 Seaco Paraformer 显示)
            hotword_input = gr.Textbox(
                label="热词 (空格分隔)", 
                placeholder="当前模型不支持热词功能",
                interactive=False,
                visible=False
            )
            
            with gr.Row():
                # 输出模式
                mode_select = gr.Dropdown(
                    choices=[("时间戳格式", "timestamp"), ("普通文本", "normal")], 
                    value="timestamp", 
                    label="输出格式"
                )
                
                # 说话人识别选项
                speaker_enable_checkbox = gr.Checkbox(
                    label="启用说话人识别", 
                    value=True,
                    visible=True
                )
            
            # 说话人数设置
            speaker_num_input = gr.Number(
                label="指定说话人数 (0=自动)", 
                value=0, 
                minimum=0, 
                step=1,
                visible=True
            )
            
            # 模型配置折叠面板
            with gr.Accordion("模型配置", open=False):
                asr_select = gr.Dropdown(
                    choices=list(asr_models.keys()), 
                    value=default_asr, 
                    label="ASR 模型"
                )
                vad_select = gr.Dropdown(
                    choices=list(vad_models_with_none.keys()), 
                    value=default_vad,
                    label="VAD 模型"
                )
                punc_select = gr.Dropdown(
                    choices=list(punc_models_with_none.keys()), 
                    value=default_punc,
                    label="标点模型"
                )
                spk_select = gr.Dropdown(
                    choices=list(spk_models_with_none.keys()), 
                    value=default_spk,
                    label="说话人模型"
                )
            
            # 状态信息显示
            info_text = gr.Textbox(
                label="状态信息", 
                value="当前使用 Paraformer 模型，支持时间戳和说话人识别", 
                interactive=False,
                lines=6
            )
            
            # 识别按钮
            submit_button = gr.Button("开始识别", variant="primary", size="lg")
        
        with gr.Column(scale=2):
            # 识别结果显示
            text_output = gr.Textbox(
                label="识别结果", 
                lines=50, 
                interactive=False,
                placeholder="识别结果将显示在这里..."
            )
    
    # 事件绑定
    model_type_select.change(
        fn=update_asr_models,
        inputs=[model_type_select],
        outputs=[asr_select, info_text]
    )
    
    model_type_select.change(
        fn=update_interface_visibility,
        inputs=[model_type_select],
        outputs=[hotword_input, speaker_enable_checkbox, speaker_num_input, punc_select, spk_select]
    )
    
    speaker_enable_checkbox.change(
        fn=update_speaker_num_visibility,
        inputs=[speaker_enable_checkbox],
        outputs=[speaker_num_input]
    )
    
    submit_button.click(
        fn=process_audio,
        inputs=[
            audio_input, device_select, model_type_select, asr_select, 
            vad_select, punc_select, spk_select, hotword_input, 
            mode_select, speaker_enable_checkbox, speaker_num_input
        ],
        outputs=[text_output, info_text, text_output]
    )

if __name__ == "__main__":
    demo.launch(inbrowser=True, server_port=7860) 